// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		584FA1D32E5EADBD009C6C8A /* AlertToast in Frameworks */ = {isa = PBXBuildFile; productRef = 584FA1D22E5EADBD009C6C8A /* AlertToast */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		58209F3F2E4B4AF20012C395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 58209F252E4B4AEE0012C395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58209F2C2E4B4AEE0012C395;
			remoteInfo = TimeScale;
		};
		58209F492E4B4AF20012C395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 58209F252E4B4AEE0012C395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58209F2C2E4B4AEE0012C395;
			remoteInfo = TimeScale;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		58209F2D2E4B4AEE0012C395 /* TimeScale.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TimeScale.app; sourceTree = BUILT_PRODUCTS_DIR; };
		58209F3E2E4B4AF20012C395 /* TimeScaleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TimeScaleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		58209F482E4B4AF20012C395 /* TimeScaleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TimeScaleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		58209F502E4B4AF20012C395 /* Exceptions for "TimeScale" folder in "TimeScale" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 58209F2C2E4B4AEE0012C395 /* TimeScale */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		58209F2F2E4B4AEE0012C395 /* TimeScale */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				58209F502E4B4AF20012C395 /* Exceptions for "TimeScale" folder in "TimeScale" target */,
			);
			path = TimeScale;
			sourceTree = "<group>";
		};
		58209F412E4B4AF20012C395 /* TimeScaleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TimeScaleTests;
			sourceTree = "<group>";
		};
		58209F4B2E4B4AF20012C395 /* TimeScaleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TimeScaleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		58209F2A2E4B4AEE0012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				584FA1D32E5EADBD009C6C8A /* AlertToast in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F3B2E4B4AF20012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F452E4B4AF20012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		58209F242E4B4AEE0012C395 = {
			isa = PBXGroup;
			children = (
				58209F2F2E4B4AEE0012C395 /* TimeScale */,
				58209F412E4B4AF20012C395 /* TimeScaleTests */,
				58209F4B2E4B4AF20012C395 /* TimeScaleUITests */,
				58209F2E2E4B4AEE0012C395 /* Products */,
			);
			sourceTree = "<group>";
		};
		58209F2E2E4B4AEE0012C395 /* Products */ = {
			isa = PBXGroup;
			children = (
				58209F2D2E4B4AEE0012C395 /* TimeScale.app */,
				58209F3E2E4B4AF20012C395 /* TimeScaleTests.xctest */,
				58209F482E4B4AF20012C395 /* TimeScaleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		58209F2C2E4B4AEE0012C395 /* TimeScale */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F512E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScale" */;
			buildPhases = (
				58209F292E4B4AEE0012C395 /* Sources */,
				58209F2A2E4B4AEE0012C395 /* Frameworks */,
				58209F2B2E4B4AEE0012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				58209F2F2E4B4AEE0012C395 /* TimeScale */,
			);
			name = TimeScale;
			packageProductDependencies = (
				584FA1D22E5EADBD009C6C8A /* AlertToast */,
			);
			productName = TimeScale;
			productReference = 58209F2D2E4B4AEE0012C395 /* TimeScale.app */;
			productType = "com.apple.product-type.application";
		};
		58209F3D2E4B4AF20012C395 /* TimeScaleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F562E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScaleTests" */;
			buildPhases = (
				58209F3A2E4B4AF20012C395 /* Sources */,
				58209F3B2E4B4AF20012C395 /* Frameworks */,
				58209F3C2E4B4AF20012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				58209F402E4B4AF20012C395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				58209F412E4B4AF20012C395 /* TimeScaleTests */,
			);
			name = TimeScaleTests;
			packageProductDependencies = (
			);
			productName = TimeScaleTests;
			productReference = 58209F3E2E4B4AF20012C395 /* TimeScaleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		58209F472E4B4AF20012C395 /* TimeScaleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F592E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScaleUITests" */;
			buildPhases = (
				58209F442E4B4AF20012C395 /* Sources */,
				58209F452E4B4AF20012C395 /* Frameworks */,
				58209F462E4B4AF20012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				58209F4A2E4B4AF20012C395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				58209F4B2E4B4AF20012C395 /* TimeScaleUITests */,
			);
			name = TimeScaleUITests;
			packageProductDependencies = (
			);
			productName = TimeScaleUITests;
			productReference = 58209F482E4B4AF20012C395 /* TimeScaleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58209F252E4B4AEE0012C395 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					58209F2C2E4B4AEE0012C395 = {
						CreatedOnToolsVersion = 16.4;
					};
					58209F3D2E4B4AF20012C395 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 58209F2C2E4B4AEE0012C395;
					};
					58209F472E4B4AF20012C395 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 58209F2C2E4B4AEE0012C395;
					};
				};
			};
			buildConfigurationList = 58209F282E4B4AEE0012C395 /* Build configuration list for PBXProject "TimeScale" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant-HK",
				"zh-Hant-TW",
				"zh-Hant-MO",
			);
			mainGroup = 58209F242E4B4AEE0012C395;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				584FA1D12E5EADBD009C6C8A /* XCRemoteSwiftPackageReference "AlertToast" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 58209F2E2E4B4AEE0012C395 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58209F2C2E4B4AEE0012C395 /* TimeScale */,
				58209F3D2E4B4AF20012C395 /* TimeScaleTests */,
				58209F472E4B4AF20012C395 /* TimeScaleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		58209F2B2E4B4AEE0012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F3C2E4B4AF20012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F462E4B4AF20012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		58209F292E4B4AEE0012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F3A2E4B4AF20012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F442E4B4AF20012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		58209F402E4B4AF20012C395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58209F2C2E4B4AEE0012C395 /* TimeScale */;
			targetProxy = 58209F3F2E4B4AF20012C395 /* PBXContainerItemProxy */;
		};
		58209F4A2E4B4AF20012C395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58209F2C2E4B4AEE0012C395 /* TimeScale */;
			targetProxy = 58209F492E4B4AF20012C395 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		58209F522E4B4AF20012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = TimeScale/TimeScale.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TimeScale/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScale;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		58209F532E4B4AF20012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = TimeScale/TimeScale.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TimeScale/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScale;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		58209F542E4B4AF20012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		58209F552E4B4AF20012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58209F572E4B4AF20012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScaleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeScale.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TimeScale";
			};
			name = Debug;
		};
		58209F582E4B4AF20012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScaleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeScale.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TimeScale";
			};
			name = Release;
		};
		58209F5A2E4B4AF20012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScaleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TimeScale;
			};
			name = Debug;
		};
		58209F5B2E4B4AF20012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.TimeScaleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TimeScale;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		58209F282E4B4AEE0012C395 /* Build configuration list for PBXProject "TimeScale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F542E4B4AF20012C395 /* Debug */,
				58209F552E4B4AF20012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F512E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScale" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F522E4B4AF20012C395 /* Debug */,
				58209F532E4B4AF20012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F562E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScaleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F572E4B4AF20012C395 /* Debug */,
				58209F582E4B4AF20012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F592E4B4AF20012C395 /* Build configuration list for PBXNativeTarget "TimeScaleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F5A2E4B4AF20012C395 /* Debug */,
				58209F5B2E4B4AF20012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		584FA1D12E5EADBD009C6C8A /* XCRemoteSwiftPackageReference "AlertToast" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/elai950/AlertToast.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.3.9;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		584FA1D22E5EADBD009C6C8A /* AlertToast */ = {
			isa = XCSwiftPackageProductDependency;
			package = 584FA1D12E5EADBD009C6C8A /* XCRemoteSwiftPackageReference "AlertToast" */;
			productName = AlertToast;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 58209F252E4B4AEE0012C395 /* Project object */;
}
