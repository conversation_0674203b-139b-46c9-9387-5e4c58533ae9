# Xcode / iOS 项目通用忽略文件

## macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
# 缩略图与垃圾桶
**/.Spotlight-V100
**/.Trashes
._*

## Xcode 构建产物与用户态文件
build/
DerivedData/
*.moved-aside
*.xccheckout
*.xcuserstate
*.xcscmblueprint

# Xcode 用户态配置（不提交）
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

## Swift / Swift Package Manager
.build/
.swiftpm/xcode/
.swiftpm/indices/
.swiftpm/cache/
# 通常建议提交 Package.resolved 以锁定依赖版本，故不忽略
# Package.resolved

## Swift Playgrounds
timeline.xctimeline
playground.xcworkspace

## Archives / 分发
*.xcarchive
*.ipa
*.dSYM
*.dSYM.zip

## CocoaPods（如需提交 Pods/，请移除此条）
Pods/

## Carthage
Carthage/Build/

## fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output/
fastlane/readme.md

## 其他 IDE/编辑器
.idea/
.vscode/
*.swp
*.swo
*.log


