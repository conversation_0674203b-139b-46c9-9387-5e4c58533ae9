# 项目总览与 MVP 路线图

本指南定义了 iOS 平台个人效率管理应用（SwiftUI + SwiftData）的产品定位、目标用户、MVP 范围与阶段性演进路线。文档面向研发团队与 AI 助手，确保可据此直接落地实现。

## 背景与目标用户画像

- **目标人群**：
  - 需要「可视化」反馈与「轻量」记录方式的学生、职场人士、独立开发者与内容创作者。
  - 对隐私敏感、偏好「本地优先（Local-first）」的数据持有方式，偶尔需要跨设备同步。

- **主要痛点**：
  - 习惯难以坚持：缺少即时反馈与正向激励。
  - 目标抽象：缺少拆分与可执行的打卡机制。
  - 时间碎片化：难以回顾一周时间去向并形成策略调整。

- **价值主张**：
  - 以 SwiftUI Charts 的可视化反馈（热力图、时间轴、分布图）驱动自我调节。
  - 本地优先的数据安全策略 + 可选 Parse Server 云同步。
  - 以 AI 提示（本地/云侧）降低坚持与复盘的认知负担。

## 产品目标

- 以「足够好用的记录」与「即时可视化」促进习惯形成、目标达成与时间管理。
- 以「最小复杂度」提供订阅制和高级功能解锁。

## 技术选型（建议）

- 移动端：SwiftUI（iOS 17+），状态管理（Observable + SwiftData 关系型建模）。
- 数据：SwiftData 本地持久化，Cloud 同步通过 Parse Server（Node.js + MongoDB）。
- 可视化：SwiftUI Charts + 自定义绘制（Canvas/Path）。
- 推送：UserNotifications（本地通知）+ APNs（远程，可选）。
- 变现：StoreKit 2（订阅 + 一次性解锁）。
- 分析与日志：统一埋点接口（本地优先，匿名脱敏上报可选）。

## MVP 范围定义（Phase 1）

- 习惯养成：
  - 创建/编辑/归档习惯，重复规则（每日/工作日/自定义）。
  - 打卡记录与 streak（连续天数）统计。
  - 日历热力图（本地计算）。
  - 本地通知（基于计划时间）。

- 目标打卡：
  - 目标创建（周期/数量/度量单位）。
  - 简单打卡与进度条展示。

- 时间分配记录器：
  - 分类 + 计时器（开始/暂停/结束），手动补录。
  - 周报：按类别/时段统计（柱状 + 饼图/环图）。

- 系统能力：
  - SwiftData 模型与迁移骨架。
  - 设置页（隐私、本地备份/恢复、订阅占位）。

不包含：账号体系、云同步、AI 生成式分析、订阅收款上架。

## Phase 2（增强与扩展）

- 云同步与账户（Parse Server）：设备间数据一致性与冲突合并策略。
- AI 提示（优先本地推断 + 轻量云函数）：
  - 习惯完成率低时的纠偏建议（提升频率/时间窗口调整）。
  - 目标拆解与阶段里程碑建议。
  - 时间周报亮点/盲点摘要。
- 推送通知增强：智能安静时段、漏打卡提醒节流。
- Charts 增强：自定义时间轴/趋势线/分布密度图。

## Phase 3（商业化与规模化）

- 订阅与高级功能解锁（StoreKit 2 + 服务器凭证校验）。
- 数据导入/导出（CSV/JSON），自动备份策略（iCloud Drive）。
- 团队模板与分享（只分享模板与统计视图，默认不分享原始数据）。
- 性能优化：大数据量（>100k 记录）下的增量查询与分页渲染。

## 风险与对策

- 隐私合规：
  - 本地优先与最小权限策略；脱敏与差分隐私（可选）。
- 同步冲突：
  - 基于时间戳 + 客户端 lastWriteWins；关键实体提供用户手动合并 UI。
- 可视化性能：
  - 预聚合缓存与按需重绘；列表虚拟化；Charts 数据降采样。
- 变现与审核：
  - 遵循 App Store 审核指南与 HIG；收敛权限与可解释性。

## 度量与成功标准

- D7 留存、习惯 21 天完成率、目标达成率、周报查看率、记录时延（交互到落盘 < 200ms）。
- 应用启动 P50 < 500ms；主线程阻塞 < 16ms；绘制帧丢失率 < 1%。


