# 开发实施指南（SwiftUI + SwiftData）

本指南提供项目初始化、关键实现建议、第三方集成与测试策略，确保 AI 助手与开发者可直接施工。

## 运行环境与依赖

- iOS 17+、Xcode 15+。
- 主要框架：SwiftUI、SwiftData、Charts、UserNotifications、StoreKit 2。
- 第三方（可选）：Parse iOS SDK（同步 Phase 2）、RevenueCat（如不自建收据校验）。

## 工程结构（建议）

```
TimeScale/
  App/
    App.swift
    AppContext.swift
  Features/
    Habits/
    Goals/
    TimeTracker/
    Insights/
    Settings/
  Services/
    NotificationService.swift
    SyncService.swift
    AIService.swift
    PurchaseService.swift
  Models/
    (SwiftData @Model 定义)
  Design/
    Colors.swift, Typography.swift, Components/
  Tests/
    Unit/, UITests/
```

## 关键实现建议

- SwiftData：
  - 使用 `@Model` + 关系；写入用集中仓储封装（如 `DataStore`），支持事务与错误统一处理。
  - 提供投影 ViewModel（轻量）供 Charts 使用，避免直接绑定大模型。

- Charts：
  - 数据准备在后台队列完成，View 层只渲染；
  - 热力图：使用 `Canvas`/`GeometryReader` 自绘网格与颜色梯度。

- 通知：
  - 首次引导解释用途再请求权限；
  - 使用 `UNUserNotificationCenter` 调度、去重、撤销过期通知。

- 订阅：
  - StoreKit 2 商品拉取、交易监听、权限缓存；
  - 服务端（Parse Cloud）校验收据并回写用户订阅等级。

- 同步（Phase 2）：
  - 以 `updatedAt` 与 `deleted` 软删除标记实现增量同步；
  - 冲突采用 `lastWriteWins`，并提供手动合并 UI。

## 示例代码（片段）

```swift
// 习惯热力图颜色映射（示意）
struct HeatmapColorScale {
    static func color(for value: Int) -> Color {
        switch value {
        case 0: return Color.gray.opacity(0.2)
        case 1: return Color.green.opacity(0.5)
        case 2: return Color.green
        case 3: return Color.blue
        default: return Color.indigo
        }
    }
}
```

```swift
// 本地通知调度（示意）
final class NotificationService {
    func scheduleDailyReminder(id: String, title: String, time: DateComponents) async throws {
        let content = UNMutableNotificationContent()
        content.title = title
        content.sound = .default

        let trigger = UNCalendarNotificationTrigger(dateMatching: time, repeats: true)
        let request = UNNotificationRequest(identifier: id, content: content, trigger: trigger)
        try await UNUserNotificationCenter.current().add(request)
    }
}
```

## 测试策略与质量保证

- 单元测试：
  - 业务逻辑（streak、周报聚合、目标进度计算）；
  - 数据迁移（模型版本升级）；
  - 通知调度与节流。

- UI 测试：
  - 关键流程：创建/打卡/撤销/计时/周报浏览。

- 性能：
  - 大数据量下的渲染基准；
  - 启动耗时与主线程卡顿监控。

## 持续集成（可选）

- Fastlane + Xcode Cloud/GitHub Actions：单测、UI 测试与 TestFlight 分发。


