# UI/UX 设计指南（iOS HIG 对齐）

## 设计系统

- 颜色：
  - 主色：系统适配（Semantic，支持浅/深色）。
  - 强调色：数据正向使用蓝/绿系；警示使用红/橙系。
  - 热力图：5 档梯度（0 → 4），避免极端对比；色盲友好调色板。

- 字体与排版：
  - 标题使用 `Title/Headline`，正文 `Body`，辅助 `Caption`；
  - 行距 1.2-1.4，段落间距与对齐随内容密度自适应。

- 图标与形状：
  - SF Symbols 优先，语义一致，图标与文字并列强化可理解性。

## 关键页面布局

- 习惯页（列表 + 热力图入口）：
  - 置顶今日待办（按提醒时间排序）；
  - 列表项左侧图标/颜色，右侧快速打卡按钮；
  - 进入详情后展示热力图与 streak 指标。

- 目标页（卡片 + 进度条）：
  - 卡片显示进度、剩余天数与快速 +1；
  - 详情页含历史趋势图与打卡记录。

- 时间页（计时器 + 周报预览）：
  - 大按钮控制开始/暂停/结束；
  - 下方显示今日累计与最近记录；
  - 周报入口展示本周概览。

- 洞察页（周报）：
  - 顶部关键 KPI（总时长、Top 分类、习惯完成率）；
  - 图表模块按重要性排序，支持收起/展开。

- 设置页：
  - 隐私与数据（导入/导出/备份）、通知、外观（主题）、订阅。

## 数据可视化规范

- Chart 选择：
  - 时间序列 → 折线/柱状；
  - 构成占比 → 环图/条形堆叠；
  - 强度分布 → 热力图（自绘）。

- 交互：
  - 悬停/点击高亮与数值提示；
  - 日期范围选择器与缩放（Phase 2）。

- 性能：
  - 数据量 > 5k 时启用降采样与虚拟化；
  - 计算放到后台队列，主线程仅做绘制。

## 动效与反馈

- 轻量反馈：微交互 + Haptics（成功、失败、轻提示）。
- 转场：保持自然与一致，避免花哨；
- 打卡完成使用小幅放大与淡入，避免打扰。

## 无障碍与国际化

- Dynamic Type、VoiceOver、足够对比度与触控目标（44pt+）。
- 日期/时间与单位（公里/英里）国际化；
- 文案简洁、术语统一。


