# 功能需求详细拆分（用户故事 + 验收标准）

本文拆分三个核心域：习惯、目标、时间记录，并补充系统级能力。每条需求配套用户故事与验收标准，供开发与测试直接落地。

## 1. 习惯养成系统

- 数据模型（摘要）：
  - Habit：名称、图标/颜色、计划规则（每日/工作日/自定义）、提醒时间窗口、是否归档、创建/更新时间。
  - HabitLog：日期、完成状态、备注、关联 Habit。

- 用户故事：
  - 作为用户，我可以创建一个「阅读 30 分钟」的习惯，并选择工作日提醒 20:00。
  - 作为用户，我可以在日历热力图上看到过去 90 天的完成强度。
  - 作为用户，我可以一键打卡与撤销打卡。

- 验收标准：
  - 支持创建/编辑/归档/删除习惯，重复规则含自定义（指定周几）。
  - 可设置 1-3 个提醒时间；触发本地通知；不重复创建历史通知。
  - 日历热力图：
    - 颜色梯度基于 0/1 或完成强度；
    - 支持滚动查看；点击某天展示日志详情。
  - Streak 计算：展示当前连续天数、历史最长连续天数。

## 2. 目标打卡系统 + AI 提示

- 数据模型（摘要）：
  - Goal：标题、度量单位（次/分钟/页等）、周期（周/月/自定义）、目标值、截止日期、提醒策略、状态。
  - GoalCheckIn：时间戳、数量、备注、关联 Goal。

- 用户故事：
  - 作为用户，我可以设立「本月跑步 60 公里」目标，并每日记录跑步公里数。
  - 作为用户，在完成率较低时收到非打扰式建议（例如周末集中补足或拆分为多次短跑）。

- 验收标准：
  - 创建/编辑/归档/删除目标；显示进度条与剩余天数。
  - 支持快速打卡（+1/+自定义量）；可撤销最近一次。
  - 简易 AI 建议（Phase 2）：基于历史完成率和时间分布提供 1-2 条可执行建议。

## 3. 时间分配记录器

- 数据模型（摘要）：
  - TimeCategory：名称、颜色、排序。
  - TimeEntry：开始时间、结束时间（或时长）、分类、标签、备注、是否计时中。

- 用户故事：
  - 作为用户，我可以为「学习/工作/娱乐/运动」等分类开启计时器，并在结束后一键保存记录。
  - 作为用户，我可以在周报中按分类查看总时长与占比。

- 验收标准：
  - 计时器具备开始/暂停/继续/结束的完整状态机，并防抖重复点击。
  - 周报：柱状图（每日时长）+ 环图（分类占比）+ Top3 分类卡片。
  - 手动补录：指定起止时间与分类，自动冲突检测（同一时间不重叠）。

## 4. 系统能力（横切关注点）

- 可视化：
  - SwiftUI Charts 组合：时间轴、分布、热力图（自绘）与环图。
  - 上万点数据下的增量渲染与降采样。

- 通知：
  - 本地通知请求权限与说明文案（HIG 友好），支持安静时段与节流策略。

- 数据：
  - 本地优先（SwiftData）；导入/导出（CSV/JSON）与备份恢复（Phase 3）。
  - 云同步（Phase 2）：Parse Server，冲突与合并策略明确。

- 变现：
  - 订阅（Pro）解锁：高级图表、AI 建议、跨设备同步、主题皮肤。

## 优先级与依赖

- P0：习惯基础、目标基础、时间记录基础、Charts 基础、本地通知、SwiftData。
- P1：AI 提示（本地规则）、云同步、Charts 增强。
- P2：订阅与高级功能、导入/导出、团队模板与分享。


