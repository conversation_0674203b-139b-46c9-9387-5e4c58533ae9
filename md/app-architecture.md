# 应用架构与原型设计

本文定义整体架构、导航流程、数据模型与组件层次；附示意图（Mermaid）。

> 注释：遵循“本地优先 + 可选云同步”的架构原则，确保隐私与性能优先。

## 架构概览

```mermaid
graph TD
  A[SwiftUI Views 层] --> B[ViewModels / Observable Objects]
  B --> C[SwiftData Models]
  C --> D[Persistence (本地存储)]
  B --> E[Services: 通知/同步/AI/支付]
  E --> F[Parse Server (可选云同步)]
  E --> G[StoreKit 2 (订阅)]
```

## 导航结构

- 根：`TabView`
  - `HabitsView`（习惯）
  - `GoalsView`（目标）
  - `TimeTrackerView`（时间）
  - `InsightsView`（洞察/周报）
  - `SettingsView`（设置）

```mermaid
flowchart LR
  Tabs[TabView] --> H[Habits]
  Tabs --> G[Goals]
  Tabs --> T[Time]
  Tabs --> I[Insights]
  Tabs --> S[Settings]
  H --> H1[Habit Detail]
  G --> G1[Goal Detail]
  T --> T1[Timer Running]
```

## 数据模型（SwiftData）

```swift
@Model
final class Habit {
    @Attribute(.unique) var id: UUID
    var name: String
    var colorHex: String
    var scheduleRule: ScheduleRule // 每日/工作日/自定义
    var reminderTimes: [DateComponents]
    var archived: Bool
    var createdAt: Date
    var updatedAt: Date
    @Relationship(deleteRule: .cascade, inverse: \HabitLog.habit) var logs: [HabitLog]

    init(name: String, colorHex: String) {
        self.id = UUID()
        self.name = name
        self.colorHex = colorHex
        self.scheduleRule = .daily
        self.reminderTimes = []
        self.archived = false
        self.createdAt = .now
        self.updatedAt = .now
        self.logs = []
    }
}

@Model
final class HabitLog {
    @Attribute(.unique) var id: UUID
    var date: Date
    var done: Bool
    var note: String?
    @Relationship var habit: Habit?
}

@Model
final class Goal {
    @Attribute(.unique) var id: UUID
    var title: String
    var unit: String // 次/分钟/公里/页
    var period: GoalPeriod // 周/月/自定义
    var target: Double
    var deadline: Date?
    var archived: Bool
    @Relationship(deleteRule: .cascade, inverse: \GoalCheckIn.goal) var checkIns: [GoalCheckIn]
}

@Model
final class GoalCheckIn {
    @Attribute(.unique) var id: UUID
    var timestamp: Date
    var amount: Double
    var note: String?
    @Relationship var goal: Goal?
}

@Model
final class TimeCategory {
    @Attribute(.unique) var id: UUID
    var name: String
    var colorHex: String
    var order: Int
}

@Model
final class TimeEntry {
    @Attribute(.unique) var id: UUID
    var start: Date
    var end: Date?
    var durationSec: Double
    var note: String?
    @Relationship var category: TimeCategory?
}
```

> 注：为 Charts 性能，需提供轻量投影（DTO）与聚合索引缓存表（Phase 2）。

## 服务与系统组件

- `NotificationService`：请求权限、调度本地提醒、重复任务去重。
- `SyncService`（Phase 2）：Parse Server，同步策略（lastWriteWins + 手动合并 UI）。
- `AIService`（Phase 2）：本地规则 + 轻量云函数，生成建议与摘要。
- `PurchaseService`：StoreKit 2 订阅/解锁，收据校验（服务端）。

## 组件层次（部分）

- Habits
  - `HabitsView` → `HabitRow` → `HabitDetailView` → `HabitCalendarHeatmap`
- Goals
  - `GoalsView` → `GoalRow` → `GoalDetailView` → `GoalProgressChart`
- Time
  - `TimeTrackerView` → `RunningTimerCard` / `TimeEntryEditor` → `WeeklyReportView`

## 状态管理与数据流

- 以 `Observable` + `@Query`（SwiftData）为主，跨域共享使用环境对象（如 `AppContext`）。
- 写入路径采取事务化封装，确保 UI 响应与持久化一致。

## 错误与健壮性

- 数据层：迁移策略（版本号 + 迁移器注册）。
- 同步层：网络错误与节流重试；冲突队列与提示。
- UI 层：空态与边界提示（无打卡、无目标时的引导）。


