请为我设计一个个人效率管理应用的完整开发指南，该应用结合数据可视化功能，面向iOS平台使用SwiftUI开发。

**应用核心功能：**
1. 习惯养成系统 + 视觉化统计（日历热力图、时间轴展示）
2. 目标打卡系统 + AI智能提示（包含推送通知和数据分析）
3. 时间分配记录器（详细记录时间使用情况，生成周报分析）

**技术栈：**
- 前端：SwiftUI (iOS)- SwiftData
- 后端：待定（请在API设计中推荐合适的技术栈,优先parse-server）
- 数据可视化：SwiftUI Charts + 自定义组件
- 变现模式：订阅制 + 高级功能解锁 + 一次性购买选项

**输出要求：**
请生成以下独立的Markdown文档文件，每个文件都应该足够详细，能够直接指导AI助手进行具体的功能开发：

1. **项目总览与MVP路线图** (`project-overview.md`)
   - 项目背景和目标用户画像
   - MVP版本功能范围定义
   - 开发阶段划分（Phase 1-3）
   - 技术选型建议和理由

2. **功能需求详细拆分** (`feature-requirements.md`)
   - 每个核心功能的详细需求描述
   - 用户故事和使用场景
   - 功能优先级排序
   - 各功能模块间的依赖关系

3. **应用架构与原型设计** (`app-architecture.md`)
   - 整体应用架构图
   - 页面流程图和导航结构
   - 数据模型设计
   - SwiftUI组件层次结构

4. **API接口设计规范** (`api-specification.md`)
   - 完整的RESTful API设计
   - 每个端点的详细说明（请求/响应格式、参数、错误处理）
   - 数据库表结构设计
   - 认证和权限管理方案

5. **UI/UX设计指南** (`ui-ux-guidelines.md`)
   - 设计系统和视觉规范
   - 关键页面的详细布局说明
   - 数据可视化组件设计要求
   - 交互动画和用户体验流程

6. **开发实施指南** (`development-guide.md`)
   - SwiftUI具体实现建议
   - 关键技术难点和解决方案
   - 第三方库推荐和集成方法
   - 测试策略和质量保证

**特殊要求：**
- 每个文档都应该包含足够的技术细节，让AI助手能够直接根据文档进行代码实现
- 重点关注功能实现的技术路径，而非项目管理时间线
- API设计要考虑未来扩展性和性能优化
- 所有设计都应该符合iOS Human Interface Guidelines
- 考虑数据隐私和本地存储优先的设计原则
- 并将关键信息、偏好添加到长期知识库`memory`中，以实现长期记忆、实现一致、和智能决策支持
- 所有生成的`.md`文件都应该放在`md/`目录下，以方便AI助手识别和管理