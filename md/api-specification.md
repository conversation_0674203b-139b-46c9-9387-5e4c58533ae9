# API 接口设计规范（REST + Parse Server 建议）

本节提供在 Parse Server 之上实现的 RESTful 风格接口约定与数据库结构。若无后端，应用在 Phase 1 可纯本地运行；Phase 2 引入同步。

> Source: 基于 Parse Server 官方文档与经验实践的接口包装建议。

## 总体设计

- 协议：HTTPS，`application/json`。
- 认证：
  - 用户登录：邮箱/Apple 登录（Sign in with Apple）。
  - 会话：Parse Session Token（或 JWT 包装）。
  - 客户端需在 `Authorization: Bearer <token>` 头中携带会话令牌。
- 版本：`/api/v1/*`
- 速率限制：IP + 用户维度（429）。
- 错误格式：`{ code: string, message: string, details?: object }`

## 资源模型

- Habit（习惯）：`id, name, colorHex, scheduleRule, reminderTimes[], archived, createdAt, updatedAt`
- HabitLog（习惯日志）：`id, habitId, date, done, note`
- Goal / GoalCheckIn：同上结构。
- TimeCategory / TimeEntry：同上结构。

## 端点定义（REST 包装）

> 注：基于 Parse Cloud Code 实现路由与权限校验，底层映射到 Parse Class。以下为建议的 REST 形态，便于跨端复用。

### 认证与账号

- POST `/api/v1/auth/login` → { email, password } | Sign in with Apple ticket
- POST `/api/v1/auth/logout`
- GET `/api/v1/auth/me` → 返回当前用户与订阅状态

### 习惯

- GET `/api/v1/habits` → 列表（支持分页/归档过滤）
- POST `/api/v1/habits` → 创建
- GET `/api/v1/habits/{id}` → 详情
- PATCH `/api/v1/habits/{id}` → 局部更新
- DELETE `/api/v1/habits/{id}` → 删除/归档

### 习惯日志

- GET `/api/v1/habits/{id}/logs?from=2024-01-01&to=2024-01-31`
- POST `/api/v1/habits/{id}/logs` → 新增 { date, done, note }
- DELETE `/api/v1/habits/{id}/logs/{logId}`

### 目标与打卡

- GET `/api/v1/goals`
- POST `/api/v1/goals`
- GET `/api/v1/goals/{id}`
- PATCH `/api/v1/goals/{id}`
- POST `/api/v1/goals/{id}/checkins` → { amount, note }
- DELETE `/api/v1/goals/{id}/checkins/{checkinId}`

### 时间记录

- GET `/api/v1/time/categories`
- POST `/api/v1/time/categories`
- PATCH `/api/v1/time/categories/{id}`
- DELETE `/api/v1/time/categories/{id}`
- GET `/api/v1/time/entries?from=...&to=...`
- POST `/api/v1/time/entries`
- PATCH `/api/v1/time/entries/{id}`
- DELETE `/api/v1/time/entries/{id}`

### 报表与洞察

- GET `/api/v1/insights/weekly` → 聚合（按分类/日）
- GET `/api/v1/insights/habits/calendar?month=2025-08` → 热力图数据

## 数据库结构（MongoDB / Parse Classes）

```text
_User(email, appleId?, subscriptionTier, createdAt, updatedAt)
Habit(userId, name, colorHex, scheduleRule, reminderTimes[], archived, createdAt, updatedAt)
HabitLog(userId, habitId, date, done, note)
Goal(userId, title, unit, period, target, deadline, archived)
GoalCheckIn(userId, goalId, timestamp, amount, note)
TimeCategory(userId, name, colorHex, order)
TimeEntry(userId, categoryId, start, end, durationSec, note)
```

索引建议：

- `Habit(userId, updatedAt)`、`HabitLog(userId, habitId, date)`、`Goal(userId)`、`TimeEntry(userId, start)`。
- 聚合表（Phase 2）：`WeeklyAgg(userId, yearWeek, buckets)`。

## 权限与安全

- 每条数据均具备 ACL：仅所属 `userId` 可读写；必要时提供受限分享令牌（只读）。
- 服务端验证 StoreKit 收据并下发订阅等级。
- 输入校验与速率限制，重要操作写入审计日志（GDPR 友好）。

## 错误码（示例）

- `AUTH_INVALID`、`AUTH_REQUIRED`、`VALIDATION_FAILED`、`NOT_FOUND`、`RATE_LIMITED`、`CONFLICT`、`SERVER_ERROR`。

## 示例请求/响应

```http
POST /api/v1/habits
Content-Type: application/json

{
  "name": "阅读",
  "colorHex": "#51C4D3",
  "scheduleRule": "workdays",
  "reminderTimes": ["20:00"]
}
```

```json
{
  "id": "hab_123",
  "name": "阅读",
  "scheduleRule": "workdays",
  "archived": false,
  "createdAt": "2025-08-12T12:00:00Z"
}
```


