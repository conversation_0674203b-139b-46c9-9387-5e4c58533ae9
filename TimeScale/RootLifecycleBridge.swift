//
//  RootLifecycleBridge.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import SwiftData

/// 根级桥接：在 App 根视图处拿到 modelContext 并做全局恢复检查
struct RootLifecycleBridge: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.scenePhase) private var scenePhase

    var body: some View {
        MainTabView()
            .onChange(of: scenePhase) { _, newPhase in
                TimeTrackingManager.shared.handleScenePhaseChange(newPhase, context: modelContext)
            }
            .task {
                // 应用启动时全局检查一次
                TimeTrackingManager.shared.checkForRecoveryOnLaunch(context: modelContext)
            }
    }
}

