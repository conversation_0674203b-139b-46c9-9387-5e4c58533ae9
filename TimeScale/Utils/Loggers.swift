//
//  Loggers.swift
//  TimeScale
//
//  集中管理应用内的 os.Logger，按功能域划分分类，便于过滤与排查。
//

import Foundation
import os

enum AppLog {
    // 子系统：优先使用 Bundle ID，若不可用则使用固定值
    private static let subsystem: String = Bundle.main.bundleIdentifier ?? "com.timescale.app"

    // 备份/恢复/同步
    static let backup = Logger(subsystem: subsystem, category: "Backup")
    // CloudKit 相关网络/存储操作
    static let cloudkit = Logger(subsystem: subsystem, category: "CloudKit")
    // 本地数据导入/导出等 IO
    static let dataIO = Logger(subsystem: subsystem, category: "DataIO")
    // UI 相关（可用于调试 Toast 行为等）
    static let ui = Logger(subsystem: subsystem, category: "UI")
}

