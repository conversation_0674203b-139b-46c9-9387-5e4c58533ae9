//
//  OrientationManager.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/28.
//

import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - 屏幕方向管理（轻量方式）
// 注意：采用 UIKit 方式控制方向。需在 iOS 17+ 下测试行为。
// 若项目不允许强制旋转，可改为仅设置横向布局而不强制方向。
enum OrientationManager {
    static func forceLandscape() {
        #if canImport(UIKit)
        let value = UIInterfaceOrientation.landscapeRight.rawValue
        UIDevice.current.setValue(value, forKey: "orientation")
        UINavigationController.attemptRotationToDeviceOrientation()
        #endif
    }

    static func forcePortrait() {
        #if canImport(UIKit)
        let value = UIInterfaceOrientation.portrait.rawValue
        UIDevice.current.setValue(value, forKey: "orientation")
        UINavigationController.attemptRotationToDeviceOrientation()
        #endif
    }
}

