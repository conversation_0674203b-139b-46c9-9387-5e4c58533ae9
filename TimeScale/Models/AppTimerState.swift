//
//  AppTimerState.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import Foundation
import SwiftData

/// 应用级计时器运行时状态（SwiftData 持久化）
/// - 仅存一条记录，用于记录进入后台时间点与恢复所需上下文
@Model
final class AppTimerState {
    @Attribute(.unique) var id: UUID
    /// 上次进入后台时间
    var lastBackgroundAt: Date?
    /// 进入后台时是否有计时在进行
    var wasRunningOnBackground: Bool
    /// 进入后台时正在运行的 TimeEntry.id
    var runningEntryId: UUID?
    /// 上次应用启动时间
    var lastLaunchAt: Date
    /// 是否检测到异常中断（例如长时间未恢复/系统杀进程）
    var abnormalKillFlag: Bool

    init() {
        self.id = UUID()
        self.wasRunningOnBackground = false
        self.lastBackgroundAt = nil
        self.runningEntryId = nil
        self.lastLaunchAt = .now
        self.abnormalKillFlag = false
    }
}

extension AppTimerState {
    /// 读取或创建唯一的 AppTimerState（便捷方法）
    @MainActor
    static func getOrCreate(in context: ModelContext) -> AppTimerState {
        if let existing = try? context.fetch(FetchDescriptor<AppTimerState>()).first {
            let state = existing
            return state
        }
        let newState = AppTimerState()
        context.insert(newState)
        try? context.save()
        return newState
    }
}

