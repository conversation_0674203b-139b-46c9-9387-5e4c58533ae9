//
//  TimeAnalysisModels.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/27.
//

import Foundation
import SwiftUI

// MARK: - 时间分析相关数据模型

/// 分类时间数据
struct CategoryTimeData: Identifiable {
    let id = UUID()
    let category: TimeCategory
    let duration: TimeInterval
}

/// 每日时间数据
struct DailyTimeData: Identifiable {
    let id = UUID()
    let date: Date
    let duration: TimeInterval
}

/// 时间图表数据
struct TimeChartData: Identifiable {
    let id = UUID()
    let label: String
    let duration: TimeInterval
}

/// 周时间数据
struct WeeklyTimeData: Identifiable {
    let id = UUID()
    let day: Date
    let duration: TimeInterval
}

// MARK: - 时间格式化工具

extension TimeInterval {
    /// 格式化时间间隔为可读字符串
    func formattedDuration() -> String {
        let hours = Int(self) / 3600
        let minutes = Int(self) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    /// 格式化为简短字符串（用于图表标签）
    func formattedShort() -> String {
        let hours = Int(self) / 3600
        let minutes = Int(self) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h\(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - 时间分析工具函数

struct TimeAnalysisUtils {
    /// 格式化时间间隔
    static func formatDuration(_ duration: TimeInterval) -> String {
        return duration.formattedDuration()
    }
    
    /// 获取周的开始日期
    static func weekStart(for date: Date = Date()) -> Date {
        let calendar = Calendar.current
        return calendar.dateInterval(of: .weekOfYear, for: date)?.start ?? date
    }
    
    /// 获取月的开始日期
    static func monthStart(for date: Date = Date()) -> Date {
        let calendar = Calendar.current
        return calendar.dateInterval(of: .month, for: date)?.start ?? date
    }
    
    /// 获取年的开始日期
    static func yearStart(for date: Date = Date()) -> Date {
        let calendar = Calendar.current
        return calendar.dateInterval(of: .year, for: date)?.start ?? date
    }
    
    /// 计算两个日期之间的天数
    static func daysBetween(_ start: Date, _ end: Date) -> Int {
        let calendar = Calendar.current
        return calendar.dateComponents([.day], from: start, to: end).day ?? 0
    }
    
    /// 获取日期范围内的所有日期
    static func dateRange(from start: Date, to end: Date) -> [Date] {
        let calendar = Calendar.current
        var dates: [Date] = []
        var currentDate = calendar.startOfDay(for: start)
        let endDate = calendar.startOfDay(for: end)
        
        while currentDate <= endDate {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        return dates
    }
}
