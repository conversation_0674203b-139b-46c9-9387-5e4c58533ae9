//
//  TimeTrackingManager.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//  计时器后台与异常恢复管理器。
//

import Foundation
import SwiftUI
import SwiftData
import UserNotifications
private enum SettingsKeys {
    static let anomalyThresholdHours = "anomalyThresholdHours"
}


@MainActor
final class TimeTrackingManager: ObservableObject {
    static let shared = TimeTrackingManager()

    @Published var pendingRecoveryEntry: TimeEntry? // 检测到需要恢复的条目
    @Published var abnormalMessage: String? // 异常提示信息

    private init() {}

    // MARK: - 后台/前台生命周期处理
    func handleScenePhaseChange(_ phase: ScenePhase, context: ModelContext) {
        let state = AppTimerState.getOrCreate(in: context)

        switch phase {
        case .active:
            // 进入前台：如果记录了后台时间且有运行中的条目，则计算后台时长影响（UI基于 currentDuration 实时计算，无需写入）
            // 检查异常：如果上次后台时间距今过长（> 12h），标记异常
            state.abnormalKillFlag = false
            if let bgAt = state.lastBackgroundAt, state.wasRunningOnBackground {
                let elapsed = Date().timeIntervalSince(bgAt)
                if elapsed > anomalyThresholdSeconds() {
                    // 超出合理范围，触发异常恢复
                    if let entry = findRunningEntry(by: state.runningEntryId, in: context) {
                        pendingRecoveryEntry = entry
                        abnormalMessage = "检测到计时在后台超过\(anomalyThresholdHours())小时，建议确认或修正。"
                    }
                }
            }
            state.lastBackgroundAt = nil
            state.wasRunningOnBackground = false
            state.runningEntryId = currentRunningEntry(in: context)?.id
            state.lastLaunchAt = .now
            try? context.save()

        case .inactive:
            break

        case .background:
            // 进入后台：记录时间与当前运行条目
            state.lastBackgroundAt = .now
            if let running = currentRunningEntry(in: context) {
                state.wasRunningOnBackground = true
                state.runningEntryId = running.id
            } else {
                state.wasRunningOnBackground = false
                state.runningEntryId = nil
            }
            try? context.save()

        @unknown default:
            break
        }
    }

    // MARK: - 启动时恢复检查
    func checkForRecoveryOnLaunch(context: ModelContext) {
        let state = AppTimerState.getOrCreate(in: context)
        // 如果存在运行中的条目但应用被系统杀死后重启，lastBackgroundAt 仍有值，或距离上次启动极久
        if let running = currentRunningEntry(in: context) {
            if let bgAt = state.lastBackgroundAt {
                let elapsed = Date().timeIntervalSince(bgAt)
                if elapsed > anomalyThresholdSeconds() {
                    pendingRecoveryEntry = running
                    abnormalMessage = "检测到计时被中断（后台超过\(anomalyThresholdHours())小时或应用异常终止）。"
                }
            }
        }
        state.lastLaunchAt = .now
        try? context.save()
    }

    // MARK: - 设置读取
    private func anomalyThresholdHours() -> Int {
        let v = UserDefaults.standard.integer(forKey: SettingsKeys.anomalyThresholdHours)
        return v > 0 ? v : 12
    }
    private func anomalyThresholdSeconds() -> TimeInterval {
        TimeInterval(anomalyThresholdHours() * 3600)
    }

    // MARK: - 本地通知（可选）
    func scheduleRunningIndicatorNotification(for entry: TimeEntry) async {
        // 给用户一个后台“正在计时”的提示（可选）
        let content = UNMutableNotificationContent()
        content.title = "正在计时"
        content.body = "类别：\(entry.category?.name ?? "未分类")"
        content.sound = .default
        content.userInfo = ["type": "timer_running", "entryId": entry.id.uuidString]

        // 1分钟后提醒一次（避免打扰，非重复）
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 60, repeats: false)
        let request = UNNotificationRequest(identifier: "timer_running_\(entry.id.uuidString)", content: content, trigger: trigger)
        do { try await UNUserNotificationCenter.current().add(request) } catch { print("通知安排失败: \(error)") }
    }

    // MARK: - 辅助
    private func currentRunningEntry(in context: ModelContext) -> TimeEntry? {
        (try? context.fetch(FetchDescriptor<TimeEntry>(predicate: #Predicate { $0.isRunning })))?.first
    }

    private func findRunningEntry(by id: UUID?, in context: ModelContext) -> TimeEntry? {
        guard let id = id else { return nil }
        let descriptor = FetchDescriptor<TimeEntry>(predicate: #Predicate { $0.id == id })
        return (try? context.fetch(descriptor))?.first
    }
}

