//
//  NotificationManager.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import Foundation
import UserNotifications
import SwiftData
import UIKit

@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    private init() {
        Task {
            await updateAuthorizationStatus()
        }
    }
    
    // MARK: - 权限管理
    
    /// 请求通知权限
    func requestAuthorization() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            await updateAuthorizationStatus()
            return granted
        } catch {
            print("请求通知权限失败: \(error)")
            return false
        }
    }
    
    /// 更新权限状态
    func updateAuthorizationStatus() async {
        let settings = await UNUserNotificationCenter.current().notificationSettings()
        authorizationStatus = settings.authorizationStatus
    }
    
    /// 打开系统设置
    func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
    
    // MARK: - 习惯提醒
    
    /// 为习惯设置提醒通知
    func scheduleHabitReminders(for habit: Habit) async {
        // 先移除旧的通知
        await removeHabitReminders(for: habit)
        
        // 检查权限
        guard authorizationStatus == .authorized else { return }
        
        // 为每个提醒时间创建通知
        for reminderTime in habit.reminderTimes {
            await scheduleHabitReminder(habit: habit, time: reminderTime)
        }
    }
    
    /// 移除习惯的所有提醒
    func removeHabitReminders(for habit: Habit) async {
        let identifiers = habit.reminderTimes.enumerated().map { index, _ in
            "habit_\(habit.id.uuidString)_\(index)"
        }
        
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }
    
    /// 创建单个习惯提醒
    private func scheduleHabitReminder(habit: Habit, time: DateComponents) async {
        let content = UNMutableNotificationContent()
        content.title = "习惯提醒"
        content.body = "该完成「\(habit.name)」了！"
        content.sound = .default
        content.badge = 1
        
        // 设置用户信息，用于处理点击事件
        content.userInfo = [
            "type": "habit_reminder",
            "habitId": habit.id.uuidString,
            "habitName": habit.name
        ]
        
        // 根据习惯的重复规则设置触发器
        let triggers = createTriggers(for: habit, time: time)
        
        for (index, trigger) in triggers.enumerated() {
            let identifier = "habit_\(habit.id.uuidString)_\(habit.reminderTimes.firstIndex(of: time) ?? 0)_\(index)"
            let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
            
            do {
                try await UNUserNotificationCenter.current().add(request)
            } catch {
                print("添加习惯提醒失败: \(error)")
            }
        }
    }
    
    /// 根据习惯重复规则创建触发器
    private func createTriggers(for habit: Habit, time: DateComponents) -> [UNCalendarNotificationTrigger] {
        var triggers: [UNCalendarNotificationTrigger] = []
        
        switch habit.scheduleRule {
        case .daily:
            // 每日提醒
            let trigger = UNCalendarNotificationTrigger(dateMatching: time, repeats: true)
            triggers.append(trigger)
            
        case .weekdays:
            // 工作日提醒（周一到周五）
            for weekday in 2...6 { // 2=周一, 6=周五
                var components = time
                components.weekday = weekday
                let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
                triggers.append(trigger)
            }
            
        case .custom:
            // 自定义日期提醒
            for weekday in habit.customWeekdays {
                var components = time
                components.weekday = weekday == 7 ? 1 : weekday + 1 // 转换为系统格式
                let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
                triggers.append(trigger)
            }
        }
        
        return triggers
    }
    
    // MARK: - 目标提醒
    
    /// 为目标设置截止提醒
    func scheduleGoalDeadlineReminder(for goal: Goal) async {
        guard let deadline = goal.deadline,
              authorizationStatus == .authorized else { return }
        
        // 移除旧的提醒
        await removeGoalReminders(for: goal)
        
        let calendar = Calendar.current
        
        // 截止前3天提醒
        if let threeDaysBefore = calendar.date(byAdding: .day, value: -3, to: deadline),
           threeDaysBefore > Date() {
            await scheduleGoalReminder(
                goal: goal,
                date: threeDaysBefore,
                title: "目标提醒",
                body: "「\(goal.title)」还有3天截止，当前进度 \(Int(goal.currentProgress * 100))%",
                identifier: "goal_\(goal.id.uuidString)_3days"
            )
        }
        
        // 截止前1天提醒
        if let oneDayBefore = calendar.date(byAdding: .day, value: -1, to: deadline),
           oneDayBefore > Date() {
            await scheduleGoalReminder(
                goal: goal,
                date: oneDayBefore,
                title: "目标即将截止",
                body: "「\(goal.title)」明天截止，当前进度 \(Int(goal.currentProgress * 100))%",
                identifier: "goal_\(goal.id.uuidString)_1day"
            )
        }
        
        // 截止当天提醒
        if deadline > Date() {
            await scheduleGoalReminder(
                goal: goal,
                date: deadline,
                title: "目标截止",
                body: "「\(goal.title)」今天截止，最终进度 \(Int(goal.currentProgress * 100))%",
                identifier: "goal_\(goal.id.uuidString)_deadline"
            )
        }
    }
    
    /// 移除目标的所有提醒
    func removeGoalReminders(for goal: Goal) async {
        let identifiers = [
            "goal_\(goal.id.uuidString)_3days",
            "goal_\(goal.id.uuidString)_1day",
            "goal_\(goal.id.uuidString)_deadline"
        ]
        
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }
    
    /// 创建目标提醒
    private func scheduleGoalReminder(goal: Goal, date: Date, title: String, body: String, identifier: String) async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.badge = 1
        
        content.userInfo = [
            "type": "goal_reminder",
            "goalId": goal.id.uuidString,
            "goalTitle": goal.title
        ]
        
        let components = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        do {
            try await UNUserNotificationCenter.current().add(request)
        } catch {
            print("添加目标提醒失败: \(error)")
        }
    }
    
    // MARK: - 通用方法
    
    /// 获取所有待发送的通知
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await UNUserNotificationCenter.current().pendingNotificationRequests()
    }
    
    /// 移除所有通知
    func removeAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    /// 清除应用角标
    func clearBadge() {
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
}

// MARK: - 通知处理扩展
extension NotificationManager {
    /// 处理通知点击事件
    func handleNotificationResponse(_ response: UNNotificationResponse) {
        let userInfo = response.notification.request.content.userInfo
        
        guard let type = userInfo["type"] as? String else { return }
        
        switch type {
        case "habit_reminder":
            if let habitId = userInfo["habitId"] as? String {
                // 处理习惯提醒点击
                print("用户点击了习惯提醒: \(habitId)")
                // 这里可以导航到对应的习惯页面
            }
            
        case "goal_reminder":
            if let goalId = userInfo["goalId"] as? String {
                // 处理目标提醒点击
                print("用户点击了目标提醒: \(goalId)")
                // 这里可以导航到对应的目标页面
            }
            
        default:
            break
        }
        
        // 清除角标
        clearBadge()
    }
}
