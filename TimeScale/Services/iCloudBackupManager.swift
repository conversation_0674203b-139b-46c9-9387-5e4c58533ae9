//
//  iCloudBackupManager.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import Foundation
import SwiftData
import CloudKit
import CryptoKit
import UIKit
import os

/// iCloud备份管理器 - 负责数据的云端备份、同步和恢复
@MainActor
final class iCloudBackupManager: ObservableObject {
    static let shared = iCloudBackupManager()

    // MARK: - 状态属性
    @Published var isBackingUp = false
    @Published var isRestoring = false
    @Published var isSyncing = false
    @Published var lastBackupDate: Date?
    @Published var lastSyncDate: Date?
    @Published var availableBackups: [BackupMetadata] = []
    @Published var iCloudAvailable = false
    @Published var syncEnabled = false

    // MARK: - 私有属性
    private let container: CKContainer
    private let database: CKDatabase
    private let backupRecordType = "TimeScaleBackup"
    private let metadataRecordType = "BackupMetadata"
    private let userDefaults = UserDefaults.standard
    private var accountChangedObserver: NSObjectProtocol?

    private init() {
        self.container = CKContainer(identifier: "iCloud.com.timescale.app")
        self.database = container.privateCloudDatabase

        loadSettings()
        checkiCloudAvailability()

        // 监听iCloud账号变更（例如用户登出/切换账号）
        // 修改 - 动态更新 iCloudAvailable 状态，提升健壮性。（备份与同步改进）
        accountChangedObserver = NotificationCenter.default.addObserver(forName: .CKAccountChanged, object: nil, queue: .main) { [weak self] _ in
            // Swift Concurrency：跨Actor调用，使用 Task @MainActor 保证隔离正确
            Task { @MainActor in
                self?.checkiCloudAvailability()
            }
        }
    }

    deinit {
        if let observer = accountChangedObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - 公共方法

    /// 执行备份到iCloud
    /// 增强：加入 os.Logger 详细日志，覆盖开始、数据准备、上传、清理、刷新、错误等节点。
    func performBackup(from modelContext: ModelContext, isIncremental: Bool = true) async throws {
        guard iCloudAvailable else {
            AppLog.backup.error("iCloud 不可用，取消备份")
            throw iCloudBackupError.iCloudUnavailable
        }

        isBackingUp = true
        defer { isBackingUp = false }

        let start = Date()
        AppLog.backup.log("开始备份 | incremental=\(isIncremental, privacy: .public)")

        do {
            // 1. 导出数据
            let exportURL = try await DataManager.shared.exportData(from: modelContext)
            let exportData = try Data(contentsOf: exportURL)
            AppLog.dataIO.log("导出完成 | url=\(exportURL.absoluteString, privacy: .private) size=\(exportData.count, privacy: .public)B")

            // 2. 创建备份元数据
            let metadata = BackupMetadata(
                id: UUID().uuidString,
                version: getCurrentAppVersion(),
                dataVersion: "1.0",
                createdAt: Date(),
                size: exportData.count,
                isIncremental: isIncremental,
                deviceName: await getDeviceName(),
                checksum: calculateChecksum(for: exportData)
            )
            AppLog.backup.log("元数据就绪 | id=\(metadata.id, privacy: .private) version=\(metadata.version, privacy: .public) device=\(metadata.deviceName, privacy: .private) checksum.prefix8=\(String(metadata.checksum.prefix(8)), privacy: .private)")

            // 3. 上传到iCloud
            try await uploadBackup(data: exportData, metadata: metadata)
            AppLog.cloudkit.log("上传完成 | id=\(metadata.id, privacy: .private)")

            // 4. 更新本地状态
            lastBackupDate = Date()
            saveSettings()
            AppLog.backup.log("状态已更新 | lastBackup=\(self.lastBackupDate ?? Date(), privacy: .public)")

            // 5. 清理旧备份（保留最近10个）
            try await cleanupOldBackups()
            AppLog.backup.log("清理旧备份完成")

            // 6. 刷新可用备份列表
            try await refreshAvailableBackups()
            AppLog.backup.log("刷新备份列表完成 | count=\(self.availableBackups.count, privacy: .public)")

            let elapsed = Date().timeIntervalSince(start)
            AppLog.backup.log("备份成功完成 | elapsed=\(elapsed, privacy: .public)s")
        } catch {
            let mapped = mapCKErrorToBackupError(error, defaultCase: .backupFailed(error.localizedDescription))
            AppLog.backup.error("备份失败 | error=\(String(describing: mapped), privacy: .public)")
            throw mapped
        }
    }

    /// 从iCloud恢复数据
    /// 增强：加入详细日志以便排查恢复失败
    func restoreFromBackup(_ metadata: BackupMetadata, to modelContext: ModelContext, replaceExisting: Bool = false) async throws {
        guard iCloudAvailable else {
            AppLog.backup.error("iCloud 不可用，取消恢复")
            throw iCloudBackupError.iCloudUnavailable
        }

        isRestoring = true
        defer { isRestoring = false }

        let start = Date()
        AppLog.backup.log("开始恢复 | id=\(metadata.id, privacy: .private) date=\(metadata.formattedDate, privacy: .public) replace=\(replaceExisting, privacy: .public)")

        do {
            // 1. 从iCloud下载备份数据
            let backupData = try await downloadBackup(metadata: metadata)
            AppLog.cloudkit.log("下载完成 | bytes=\(backupData.count, privacy: .public)")

            // 2. 验证数据完整性
            let downloadedChecksum = calculateChecksum(for: backupData)
            guard downloadedChecksum == metadata.checksum else {
                AppLog.backup.error("校验失败 | expected=\(metadata.checksum.prefix(8), privacy: .private) got=\(downloadedChecksum.prefix(8), privacy: .private)")
                throw iCloudBackupError.dataCorrupted
            }
            AppLog.backup.log("校验通过")

            // 3. 检查版本兼容性
            try validateVersionCompatibility(metadata.dataVersion)
            AppLog.backup.log("版本兼容 | dataVersion=\(metadata.dataVersion, privacy: .public)")

            // 4. 创建临时文件
            let tempURL = FileManager.default.temporaryDirectory
                .appendingPathComponent("restore_\(metadata.id).json")
            try backupData.write(to: tempURL)
            AppLog.dataIO.log("临时文件写入完成 | url=\(tempURL.path, privacy: .private)")

            // 5. 导入数据
            try await DataManager.shared.importData(
                from: tempURL,
                to: modelContext,
                replaceExisting: replaceExisting
            )
            AppLog.dataIO.log("导入完成")

            // 6. 清理临时文件
            try? FileManager.default.removeItem(at: tempURL)
            AppLog.dataIO.log("清理临时文件完成")

            AppLog.backup.log("恢复成功 | elapsed=\(Date().timeIntervalSince(start), privacy: .public)s")
        } catch {
            AppLog.backup.error("恢复失败 | error=\(String(describing: error), privacy: .public)")
            throw iCloudBackupError.restoreFailed(error.localizedDescription)
        }
    }

    /// 启用/禁用自动同步
    func setSyncEnabled(_ enabled: Bool) {
        syncEnabled = enabled
        saveSettings()
        AppLog.backup.log("设置自动同步 | enabled=\(enabled, privacy: .public)")

        if enabled {
            Task {
                do {
                    try await performAutoSync()
                } catch {
                    AppLog.backup.error("自动同步触发失败 | error=\(String(describing: error), privacy: .public)")
                }
            }
        }
    }

    /// 执行自动同步
    func performAutoSync() async throws {
        guard syncEnabled && iCloudAvailable else {
            AppLog.backup.log("跳过自动同步 | enabled=\(self.syncEnabled, privacy: .public) iCloud=\(self.iCloudAvailable, privacy: .public)")
            return
        }

        isSyncing = true
        defer { isSyncing = false }

        // 检查是否需要同步（距离上次同步超过1小时）
        if let lastSync = lastSyncDate,
           Date().timeIntervalSince(lastSync) < 3600 {
            AppLog.backup.log("自动同步节流：距离上次小于1小时 | lastSync=\(lastSync, privacy: .public)")
            return
        }

        // 执行增量备份作为同步
        // 这里可以根据需要实现更复杂的同步逻辑
        AppLog.backup.log("开始执行自动同步（增量备份占位）")
        lastSyncDate = Date()
        saveSettings()
        AppLog.backup.log("自动同步完成 | lastSync=\(self.lastSyncDate ?? Date(), privacy: .public)")
    }

    /// 刷新可用备份列表
    func refreshAvailableBackups() async throws {
        guard iCloudAvailable else {
            AppLog.cloudkit.error("刷新列表跳过：iCloud 不可用")
            return
        }

        let query = CKQuery(recordType: metadataRecordType, predicate: NSPredicate(value: true))
        // 修改 - 使用系统字段 creationDate 排序，避免CloudKit Dashboard未建索引导致的查询错误（获取备份列表失败）
        // Confirmed via mcp-feedback-enhanced
        query.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]

        do {
            AppLog.cloudkit.log("开始刷新备份列表")
            let (records, _) = try await database.records(matching: query)
            AppLog.cloudkit.log("CloudKit 返回记录 | count=\(records.count, privacy: .public)")

            availableBackups = records.compactMap { (_, result) in
                switch result {
                case .success(let record):
                    return BackupMetadata(from: record)
                case .failure(let err):
                    AppLog.cloudkit.error("记录解析失败 | error=\(String(describing: err), privacy: .public)")
                    return nil
                }
            }
        } catch {
            AppLog.cloudkit.error("刷新备份列表失败 | error=\(String(describing: error), privacy: .public)")
            throw mapCKErrorToBackupError(error, defaultCase: .fetchFailed(error.localizedDescription))
        }
    }

    /// 删除指定备份
    func deleteBackup(_ metadata: BackupMetadata) async throws {
        guard iCloudAvailable else {
            AppLog.cloudkit.error("删除跳过：iCloud 不可用")
            return
        }

        AppLog.cloudkit.log("删除备份 | id=\(metadata.id, privacy: .private)")
        let metadataRecordID = CKRecord.ID(recordName: metadata.id)
        let backupRecordID = CKRecord.ID(recordName: "backup_\(metadata.id)")

        try await database.deleteRecord(withID: metadataRecordID)
        try await database.deleteRecord(withID: backupRecordID)
        AppLog.cloudkit.log("删除完成 | id=\(metadata.id, privacy: .private)")

        // 从本地列表中移除
        availableBackups.removeAll { $0.id == metadata.id }
    }

    // MARK: - 私有方法

    private func checkiCloudAvailability() {
        Task {
            do {
                let status = try await container.accountStatus()
                await MainActor.run {
                    self.iCloudAvailable = (status == .available)
                    AppLog.cloudkit.log("iCloud 可用性 | status=\(status.rawValue, privacy: .public) available=\(self.iCloudAvailable, privacy: .public)")
                }
            } catch {
                await MainActor.run {
                    self.iCloudAvailable = false
                    AppLog.cloudkit.error("查询 iCloud 可用性失败 | error=\(String(describing: error), privacy: .public)")
                }
            }
        }
    }

    private func uploadBackup(data: Data, metadata: BackupMetadata) async throws {
        // 1. 创建备份数据记录
        let tempURL = try createTempFile(with: data)
        AppLog.dataIO.log("临时文件创建 | url=\(tempURL.path, privacy: .private) size=\(data.count, privacy: .public)B")
        let backupRecord = CKRecord(recordType: backupRecordType, recordID: CKRecord.ID(recordName: "backup_\(metadata.id)"))
        backupRecord["data"] = CKAsset(fileURL: tempURL)

        // 2. 创建元数据记录
        let metadataRecord = CKRecord(recordType: metadataRecordType, recordID: CKRecord.ID(recordName: metadata.id))
        metadataRecord["version"] = metadata.version
        metadataRecord["dataVersion"] = metadata.dataVersion
        metadataRecord["createdAt"] = metadata.createdAt
        metadataRecord["size"] = metadata.size
        metadataRecord["isIncremental"] = metadata.isIncremental ? 1 : 0
        metadataRecord["deviceName"] = metadata.deviceName
        metadataRecord["checksum"] = metadata.checksum

        // 3. 批量上传
        let operation = CKModifyRecordsOperation(recordsToSave: [backupRecord, metadataRecord])
        operation.savePolicy = .allKeys
        operation.perRecordProgressBlock = { recordID, progress in
            AppLog.cloudkit.log("上传进度 | record=\(recordID.recordID.recordName, privacy: .private) progress=\(progress, privacy: .public)")
        }
        operation.perRecordSaveBlock = { recordID, result in
            switch result {
            case .success:
                AppLog.cloudkit.log("单记录保存成功 | record=\(recordID.recordName, privacy: .private)")
            case .failure(let error):
                AppLog.cloudkit.error("单记录保存失败 | record=\(recordID.recordName, privacy: .private) error=\(String(describing: error), privacy: .public)")
            }
        }

        try await withCheckedThrowingContinuation { continuation in
            operation.modifyRecordsResultBlock = { result in
                switch result {
                case .success:
                    AppLog.cloudkit.log("批量上传成功 | id=\(metadata.id, privacy: .private)")
                    continuation.resume()
                case .failure(let error):
                    AppLog.cloudkit.error("批量上传失败 | id=\(metadata.id, privacy: .private) error=\(String(describing: error), privacy: .public)")
                    continuation.resume(throwing: error)
                }
            }
            database.add(operation)
        }
    }

    private func downloadBackup(metadata: BackupMetadata) async throws -> Data {
        let recordID = CKRecord.ID(recordName: "backup_\(metadata.id)")
        AppLog.cloudkit.log("开始下载备份 | record=\(recordID.recordName, privacy: .private)")
        let record = try await database.record(for: recordID)

        guard let asset = record["data"] as? CKAsset,
              let fileURL = asset.fileURL else {
            AppLog.cloudkit.error("下载失败：缺少数据资产")
            throw iCloudBackupError.downloadFailed("备份文件不存在")
        }

        let data = try Data(contentsOf: fileURL)
        AppLog.cloudkit.log("下载结束 | bytes=\(data.count, privacy: .public)")
        return data
    }

    private func createTempFile(with data: Data) throws -> URL {
        let tempURL = FileManager.default.temporaryDirectory
            .appendingPathComponent(UUID().uuidString)
            .appendingPathExtension("json")
        try data.write(to: tempURL)
        return tempURL
    }

    private func calculateChecksum(for data: Data) -> String {
        return data.sha256
    }

    private func getCurrentAppVersion() -> String {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }

    private func getDeviceName() async -> String {
        return UIDevice.current.name
    }

    private func validateVersionCompatibility(_ dataVersion: String) throws {
        // 简单的版本兼容性检查
        guard dataVersion.hasPrefix("1.") else {
            throw iCloudBackupError.incompatibleVersion(dataVersion)
        }
    }

    private func cleanupOldBackups() async throws {
        // 保留最近10个备份，删除更老的
        let sortedBackups = availableBackups.sorted { $0.createdAt > $1.createdAt }
        let backupsToDelete = Array(sortedBackups.dropFirst(10))

        for backup in backupsToDelete {
            try await deleteBackup(backup)
        }
    }


    // MARK: - 错误映射
    /// 将 CloudKit 的错误映射为更可读的 iCloudBackupError
    private func mapCKErrorToBackupError(_ error: Error, defaultCase: iCloudBackupError) -> iCloudBackupError {
        // 尝试识别 CKError
        if let ckError = error as? CKError {
            switch ckError.code {
            case .notAuthenticated:
                return .iCloudUnavailable
            case .networkUnavailable, .networkFailure:
                return .networkError
            case .quotaExceeded:
                return .quotaExceeded
            case .permissionFailure, .accountTemporarilyUnavailable:
                return .iCloudUnavailable
            default:
                return defaultCase
            }
        }
        return defaultCase
    }

    private func loadSettings() {
        lastBackupDate = userDefaults.object(forKey: "lastBackupDate") as? Date
        lastSyncDate = userDefaults.object(forKey: "lastSyncDate") as? Date
        syncEnabled = userDefaults.bool(forKey: "syncEnabled")
    }

    private func saveSettings() {
        if let lastBackupDate = lastBackupDate {
            userDefaults.set(lastBackupDate, forKey: "lastBackupDate")
        }
        if let lastSyncDate = lastSyncDate {
            userDefaults.set(lastSyncDate, forKey: "lastSyncDate")
        }
        userDefaults.set(syncEnabled, forKey: "syncEnabled")
    }
}

// MARK: - 数据结构

/// 备份元数据
struct BackupMetadata: Identifiable, Codable {
    let id: String
    let version: String          // 应用版本
    let dataVersion: String      // 数据格式版本
    let createdAt: Date
    let size: Int               // 备份文件大小（字节）
    let isIncremental: Bool     // 是否为增量备份
    let deviceName: String      // 创建备份的设备名称
    let checksum: String        // 数据校验和

    init(id: String, version: String, dataVersion: String, createdAt: Date,
         size: Int, isIncremental: Bool, deviceName: String, checksum: String) {
        self.id = id
        self.version = version
        self.dataVersion = dataVersion
        self.createdAt = createdAt
        self.size = size
        self.isIncremental = isIncremental
        self.deviceName = deviceName
        self.checksum = checksum
    }

    /// 从CloudKit记录创建
    init?(from record: CKRecord) {
        guard let version = record["version"] as? String,
              let dataVersion = record["dataVersion"] as? String,
              let size = record["size"] as? Int,
              let isIncrementalValue = record["isIncremental"] as? Int,
              let deviceName = record["deviceName"] as? String,
              let checksum = record["checksum"] as? String else {
            return nil
        }

        // 修改 - createdAt 兼容：若自定义字段不存在，则回退使用系统 creationDate
        let createdAtField = record["createdAt"] as? Date
        let fallbackCreation = record.creationDate // CKRecord 系统字段
        let finalCreatedAt = createdAtField ?? fallbackCreation ?? Date()

        self.id = record.recordID.recordName
        self.version = version
        self.dataVersion = dataVersion
        self.createdAt = finalCreatedAt
        self.size = size
        self.isIncremental = isIncrementalValue == 1
        self.deviceName = deviceName
        self.checksum = checksum
    }

    /// 格式化文件大小
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(size))
    }

    /// 格式化创建时间
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}

/// iCloud备份错误类型
enum iCloudBackupError: LocalizedError {
    case iCloudUnavailable
    case backupFailed(String)
    case restoreFailed(String)
    case downloadFailed(String)
    case fetchFailed(String)
    case dataCorrupted
    case incompatibleVersion(String)
    case quotaExceeded
    case networkError

    var errorDescription: String? {
        switch self {
        case .iCloudUnavailable:
            return "iCloud不可用，请检查网络连接和iCloud设置"
        case .backupFailed(let message):
            return "备份失败：\(message)"
        case .restoreFailed(let message):
            return "恢复失败：\(message)"
        case .downloadFailed(let message):
            return "下载失败：\(message)"
        case .fetchFailed(let message):
            return "获取备份列表失败：\(message)"
        case .dataCorrupted:
            return "备份数据已损坏，无法恢复"
        case .incompatibleVersion(let version):
            return "备份版本(\(version))与当前应用不兼容"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .networkError:
            return "网络连接错误"
        }
    }
}

// MARK: - 扩展

extension Data {
    /// 计算SHA256校验和
    var sha256: String {
        let hashed = SHA256.hash(data: self)
        return hashed.compactMap { String(format: "%02x", $0) }.joined()
    }
}
