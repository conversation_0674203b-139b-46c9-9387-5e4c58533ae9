//
//  MainTabView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI

struct MainTabView: View {
    @StateObject private var tabRouter = TabRouter()
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: Binding(
                get: { tabRouter.selectedTab },
                set: { tabRouter.selectedTab = $0; selectedTab = $0 }
            )) {
            // 习惯页面
            HabitsView()
                .tabItem {
                    Image(systemName: "checkmark.circle")
                    Text(LocalizedStringKey("习惯"))
                }
                .tag(0)

            // 目标页面
            GoalsView()
                .tabItem {
                    Image(systemName: "target")
                    Text(LocalizedStringKey("目标"))
                }
                .tag(1)

            // 时间记录页面
            TimeTrackerView()
                .tabItem {
                    Image(systemName: "timer")
                    Text(LocalizedStringKey("时间"))
                }
                .tag(2)

            // 洞察页面
            InsightsView()
                .tabItem {
                    Image(systemName: "chart.bar")
                    Text(LocalizedStringKey("洞察"))
                }
                .tag(3)

            // 设置页面
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text(LocalizedStringKey("设置"))
                }
                .tag(4)
        .task {
            // 全局一次恢复检查，确保从任意 Tab 进入也可触发
            // 注意：这里无法直接拿到 ModelContext；交由任一含有 modelContext 的页面执行即可。
        }
        }
        .accentColor(.blue)
        .environmentObject(tabRouter)
    }
}

#Preview {
    MainTabView()
        .modelContainer(for: [Habit.self, Goal.self, TimeCategory.self], inMemory: true)
}
