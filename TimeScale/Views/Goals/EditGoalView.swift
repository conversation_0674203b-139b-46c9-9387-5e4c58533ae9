//
//  EditGoalView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import SwiftData

// 新增 - 目标编辑页，支持标题、周期、数量+单位、截止时间、归档与删除。Confirmed via mcp-feedback-enhanced
struct EditGoalView: View {
    let goal: Goal
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    // 可编辑状态
    @State private var title: String = ""
    @State private var period: GoalPeriod = .weekly
    @State private var target: Double = 1
    @State private var unit: String = "次"
    @State private var hasDeadline: Bool = false
    @State private var deadline: Date? = nil
    @State private var isArchived: Bool = false
    @State private var didLoad = false

    private let commonUnits = ["次", "分钟", "小时", "公里", "页", "杯", "个"]

    var body: some View {
        NavigationView {
            Form {
                Section("基本信息") {
                    TextField("目标名称", text: $title)
                }

                Section("目标设置") {
                    Picker("周期", selection: $period) {
                        ForEach(GoalPeriod.allCases, id: \.self) { p in
                            Text(p.displayName).tag(p)
                        }
                    }
                    .pickerStyle(.segmented)

                    HStack {
                        Text("目标数量")
                        Spacer()
                        TextField("数量", value: $target, format: .number)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                        Picker("单位", selection: $unit) {
                            ForEach(commonUnits, id: \.self) { u in
                                Text(u).tag(u)
                            }
                        }
                        .pickerStyle(.menu)
                    }
                }

                Section("截止时间") {
                    Toggle("设置截止时间", isOn: $hasDeadline)
                    if hasDeadline {
                        DatePicker("截止日期", selection: Binding(
                            get: { deadline ?? Date() },
                            set: { deadline = $0 }
                        ), displayedComponents: .date)
                    }
                }

                Section("其他") {
                    Toggle("归档该目标", isOn: $isArchived)
                    Button(role: .destructive) {
                        deleteGoal()
                    } label: {
                        Text("删除目标")
                    }
                }
            }
            .navigationTitle("编辑目标")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") { saveChanges() }
                        .disabled(title.isEmpty || target <= 0)
                }
            }
            .onAppear { loadIfNeeded() }
        }
    }

    private func loadIfNeeded() {
        guard !didLoad else { return }
        title = goal.title
        period = goal.period
        target = goal.target
        unit = goal.unit
        hasDeadline = goal.deadline != nil
        deadline = goal.deadline
        isArchived = goal.archived
        didLoad = true
    }

    private func saveChanges() {
        goal.title = title
        goal.period = period
        goal.target = target
        goal.unit = unit
        goal.deadline = hasDeadline ? deadline : nil
        goal.archived = isArchived
        goal.updatedAt = .now
        try? modelContext.save()
        dismiss()
    }

    private func deleteGoal() {
        modelContext.delete(goal)
        try? modelContext.save()
        dismiss()
    }
}

