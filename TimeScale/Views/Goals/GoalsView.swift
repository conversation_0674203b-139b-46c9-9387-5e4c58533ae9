//
//  GoalsView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData

struct GoalsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(filter: #Predicate<Goal> { !$0.archived }, sort: \Goal.createdAt) 
    private var goals: [Goal]
    
    @State private var showingAddGoal = false
    
    var body: some View {
        NavigationView {
            VStack {
                if goals.isEmpty {
                    // 空状态
                    VStack(spacing: 20) {
                        Image(systemName: "target")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text(LocalizedStringKey("还没有目标"))
                            .font(.title2)
                            .fontWeight(.medium)

                        Text(LocalizedStringKey("设定你的第一个目标，开始追踪进度"))
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        Button(LocalizedStringKey("创建目标")) {
                            showingAddGoal = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 目标列表
                    List {
                        ForEach(goals) { goal in
                            NavigationLink(destination: GoalDetailView(goal: goal)) {
                                GoalRowView(goal: goal)
                            }
                        }
                        .onDelete(perform: deleteGoals)
                    }
                }
            }
            .navigationTitle(LocalizedStringKey("目标"))
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddGoal = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddGoal) {
                CreateGoalView()
            }
        }
    }
    
    private func deleteGoals(offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                let goal = goals[index]
                goal.archived = true
                goal.updatedAt = .now
            }
            try? modelContext.save()
        }
    }
}

// MARK: - 目标行视图
struct GoalRowView: View {
    let goal: Goal
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(goal.title)
                    .font(.headline)
                
                Spacer()
                
                Text(goal.period.displayName)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.2))
                    .cornerRadius(4)
            }
            
            // 激励导向：本期完成 + 达成/超越提示（弱化百分比）
            VStack(alignment: .leading, spacing: 6) {
                HStack(spacing: 8) {
                    Text("本期完成 \(Int(goal.periodCompletedAmount))\(goal.unit)")
                        .font(.caption)
                        .foregroundColor(.primary)

                    if goal.isAchieved {
                        Label("已达成", systemImage: "checkmark.seal.fill")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green.opacity(0.15))
                            .foregroundColor(.green)
                            .cornerRadius(6)
                    }

                    if goal.exceedAmount > 0 {
                        Label("超越 +\(Int(goal.exceedAmount))", systemImage: "arrow.up.right.circle.fill")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.15))
                            .foregroundColor(.orange)
                            .cornerRadius(6)
                    }

                    Spacer()
                }

                // 细提示条（2pt）
                ZStack(alignment: .leading) {
                    Capsule().fill(Color.gray.opacity(0.2)).frame(height: 2)
                    let progress = min(goal.periodCompletedAmount / max(1, goal.target), 1.0)
                    Capsule().fill(Color.blue).frame(width: max(2, progress * 120), height: 2)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 临时占位视图
struct GoalDetailView: View {
    let goal: Goal
    @Environment(\.modelContext) private var modelContext

    @State private var showingCheckIn = false
    @State private var checkInAmount: Double = 1
    @State private var showingEdit = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 进度卡片
                progressCard

                // 快速打卡
                quickCheckInSection

                // 最近打卡记录
                recentCheckInsSection
            }
            .padding()
        }
        .navigationTitle(goal.title)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack {
                    Button("编辑") { showingEdit = true }
                    Button("打卡") { showingCheckIn = true }
                }
            }
        }
        .sheet(isPresented: $showingCheckIn) {
            GoalCheckInView(goal: goal)
        }
        .sheet(isPresented: $showingEdit) {
            EditGoalView(goal: goal)
        }
    }

    // MARK: - 进度卡片（激励导向）
    private var progressCard: some View {
        VStack(spacing: 14) {
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("本期完成")
                        .font(.headline)
                    HStack(spacing: 8) {
                        Text("\(Int(goal.periodCompletedAmount)) \(goal.unit)")
                            .font(.title2)
                            .fontWeight(.bold)
                        if goal.isAchieved {
                            Label("已达成", systemImage: "checkmark.seal.fill")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.green.opacity(0.15))
                                .foregroundColor(.green)
                                .cornerRadius(6)
                        }
                        if goal.exceedAmount > 0 {
                            Label("超越 +\(Int(goal.exceedAmount))", systemImage: "arrow.up.right.circle.fill")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange.opacity(0.15))
                                .foregroundColor(.orange)
                                .cornerRadius(6)
                        }
                    }
                    if goal.target > 0 {
                        Text("目标 \(Int(goal.target)) \(goal.unit)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text("自由目标")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                Spacer()
                // 保留小型进度环，弱化存在感
                CircularProgressView(progress: goal.currentProgress)
                    .frame(width: 48, height: 48)
            }

            // 细提示条（2pt）
            ZStack(alignment: .leading) {
                Capsule().fill(Color.gray.opacity(0.2)).frame(height: 2)
                let progress = min(goal.periodCompletedAmount / max(1, goal.target), 1.0)
                Capsule().fill(Color.blue).frame(width: max(2, progress * 200), height: 2)
            }

            // 连续与里程碑
            HStack(spacing: 8) {
                if goal.achievedStreakCount > 0 {
                    Label("连续达标 \(goal.achievedStreakCount)", systemImage: "flame.fill")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(Color.red.opacity(0.1))
                        .foregroundColor(.red)
                        .cornerRadius(6)
                }
                ForEach(goal.achievedMilestones, id: \.self) { m in
                    Label("里程碑 \(m)%", systemImage: "star.fill")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(Color.yellow.opacity(0.15))
                        .foregroundColor(.orange)
                        .cornerRadius(6)
                }
                Spacer()
                if let deadline = goal.deadline {
                    Text("截止: \(deadline, format: .dateTime.month().day())")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }

    // MARK: - 快速打卡
    private var quickCheckInSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快速打卡")
                .font(.headline)

            HStack(spacing: 12) {
                ForEach([1, 5, 10], id: \.self) { amount in
                    Button("+\(amount)") {
                        quickCheckIn(amount: Double(amount))
                    }
                    .buttonStyle(.bordered)
                }

                Spacer()
            }
        }
    }

    // MARK: - 最近打卡记录
    private var recentCheckInsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近记录")
                .font(.headline)

            LazyVStack(spacing: 8) {
                ForEach(recentCheckIns, id: \.id) { checkIn in
                    GoalCheckInRowView(checkIn: checkIn)
                }
            }
        }
    }

    // MARK: - 计算属性
    private var recentCheckIns: [GoalCheckIn] {
        goal.checkIns
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(10)
            .map { $0 }
    }

    // MARK: - 方法
    private func quickCheckIn(amount: Double) {
        let checkIn = GoalCheckIn(amount: amount)
        checkIn.goal = goal
        modelContext.insert(checkIn)
        try? modelContext.save()
    }

    private func deleteCheckIn(_ checkIn: GoalCheckIn) {
        modelContext.delete(checkIn)
        try? modelContext.save()
    }
}

struct CreateGoalView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @State private var title = ""
    @State private var unit = "次"
    @State private var period = GoalPeriod.weekly
    @State private var target: Double = 1
    @State private var deadline: Date?
    @State private var hasDeadline = false

    // 常用单位列表，供内嵌选择使用
    private let commonUnits = ["次", "分钟", "小时", "公里", "页", "杯", "个"]

    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section("基本信息") {
                    TextField("目标名称", text: $title)
                }

                // 目标设置
                Section("目标设置") {
                    // 周期选择（分段控件）
                    Picker("周期", selection: $period) {
                        ForEach(GoalPeriod.allCases, id: \.self) { period in
                            Text(period.displayName).tag(period)
                        }
                    }
                    .pickerStyle(.segmented)

                    // 目标数量 + 单位（内联选择，避免“单位”重复）
                    HStack {
                        Text("目标数量")
                        Spacer()
                        TextField("数量", value: $target, format: .number)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                        Picker("单位", selection: $unit) {
                            ForEach(commonUnits, id: \.self) { unit in
                                Text(unit).tag(unit)
                            }
                        }
                        .pickerStyle(.menu)
                    }
                }

                // 截止时间（可选）
                Section("截止时间") {
                    Toggle("设置截止时间", isOn: $hasDeadline)

                    if hasDeadline {
                        DatePicker("截止日期", selection: Binding(
                            get: { deadline ?? Date() },
                            set: { deadline = $0 }
                        ), displayedComponents: .date)
                    }
                }
            }
            .navigationTitle("新建目标")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") { saveGoal() }
                        .disabled(title.isEmpty)
                }
            }
        }
    }

    private func saveGoal() {
        let goal = Goal(
            title: title,
            unit: unit,
            period: period,
            target: 0, // 取消在新建时设置数量，默认自由目标
            deadline: hasDeadline ? deadline : nil
        )
        modelContext.insert(goal)
        try? modelContext.save()
        dismiss()
    }
}

#Preview {
    GoalsView()
        .modelContainer(for: [Goal.self, GoalCheckIn.self], inMemory: true)
}
