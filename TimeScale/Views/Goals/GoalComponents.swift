//
//  GoalComponents.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData

// MARK: - 圆形进度视图
struct CircularProgressView: View {
    let progress: Double

    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 6)

            Circle()
                .trim(from: 0, to: progress)
                .stroke(Color.blue, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: progress)

            Text("\(Int(progress * 100))%")
                .font(.caption)
                .fontWeight(.bold)
        }
    }
}

// MARK: - 目标打卡记录行视图
struct GoalCheckInRowView: View {
    let checkIn: GoalCheckIn
    let onDelete: (() -> Void)?

    init(checkIn: GoalCheckIn, onDelete: (() -> Void)? = nil) {
        self.checkIn = checkIn
        self.onDelete = onDelete
    }


    private var timeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        return formatter
    }

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(timeFormatter.string(from: checkIn.timestamp))
                    .font(.body)

                if let note = checkIn.note, !note.isEmpty {
                    Text(note)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            HStack(spacing: 8) {
                Text("+\(checkIn.amount.formatted())")
                    .font(.headline)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)

                if let onDelete = onDelete {
                    Button {
                        onDelete()
                    } label: {
                        Label("撤销", systemImage: "arrow.uturn.left")
                            .labelStyle(.titleAndIcon)
                    }
                    .font(.caption)
                    .buttonStyle(.bordered)
                    .tint(.red)
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 目标打卡视图
struct GoalCheckInView: View {
    let goal: Goal
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @State private var amount: Double = 1
    @State private var note = ""

    var body: some View {
        NavigationView {
            Form {
                Section("打卡信息") {
                    HStack {
                        Text("数量")
                        Spacer()
                        TextField("数量", value: $amount, format: .number)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                        Text(goal.unit)
                            .foregroundColor(.secondary)
                    }

                    TextField("备注（可选）", text: $note, axis: .vertical)
                        .lineLimit(3)
                }

                if goal.target > 0 {
                    Section("目标信息") {
                        HStack {
                            Text("目标")
                            Spacer()
                            Text("\(Int(goal.target)) \(goal.unit)")
                                .foregroundColor(.secondary)
                        }

                        HStack {
                            Text("当前进度")
                            Spacer()
                            Text("\(Int(goal.currentProgress * goal.target))/\(Int(goal.target))")
                                .foregroundColor(.secondary)
                        }

                        HStack {
                            Text("完成后进度")
                            Spacer()
                            Text("\(Int((goal.currentProgress * goal.target) + amount))/\(Int(goal.target))")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .navigationTitle("打卡")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        saveCheckIn()
                    }
                    .disabled(amount <= 0)
                }
            }
        }
    }

    private func saveCheckIn() {
        let checkIn = GoalCheckIn(amount: amount, note: note.isEmpty ? nil : note)
        checkIn.goal = goal
        modelContext.insert(checkIn)
        try? modelContext.save()

        dismiss()
    }
}

#Preview("CircularProgressView") {
    CircularProgressView(progress: 0.75)
        .frame(width: 100, height: 100)
        .padding()
}

#Preview("GoalCheckInRowView") {
    let checkIn = GoalCheckIn(amount: 5, note: "完成了5公里跑步")

    GoalCheckInRowView(checkIn: checkIn, onDelete: {})
        .padding()
}

#Preview("GoalCheckInView") {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Goal.self, GoalCheckIn.self, configurations: config)

    let goal = Goal(title: "跑步", unit: "公里", period: .weekly, target: 20)
    container.mainContext.insert(goal)

    return GoalCheckInView(goal: goal)
        .modelContainer(container)
}
