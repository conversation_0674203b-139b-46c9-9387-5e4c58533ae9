//
//  TimerRecoverySheet.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import SwiftData

/// 计时异常恢复弹窗
struct TimerRecoverySheet: View {
    let entry: TimeEntry
    let message: String
    let onContinue: () -> Void
    let onAdjust: (_ start: Date, _ end: Date) -> Void
    let onDiscard: () -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var start: Date
    @State private var end: Date

    init(entry: TimeEntry, message: String, onContinue: @escaping () -> Void, onAdjust: @escaping (_ start: Date, _ end: Date) -> Void, onDiscard: @escaping () -> Void) {
        self.entry = entry
        self.message = message
        self.onContinue = onContinue
        self.onAdjust = onAdjust
        self.onDiscard = onDiscard
        _start = State(initialValue: entry.start)
        _end = State(initialValue: Date())
    }

    var body: some View {
        NavigationView {
            Form {
                Section("异常情况") {
                    Text(message).foregroundStyle(.orange)
                }

                Section("当前条目") {
                    HStack { Text("分类"); Spacer(); Text(entry.category?.name ?? "未分类") }
                    HStack { Text("开始时间"); Spacer(); Text(format(date: entry.start)) }
                    if let end = entry.end { HStack { Text("结束时间"); Spacer(); Text(format(date: end)) } }
                    HStack { Text("当前时长"); Spacer(); Text(format(duration: entry.currentDuration)) }
                }

                Section("手动调整") {
                    DatePicker("开始时间", selection: $start, displayedComponents: [.date, .hourAndMinute])
                    DatePicker("结束时间", selection: $end, in: start...Date(), displayedComponents: [.date, .hourAndMinute])
                }

                Section {
                    Button("继续计时") { onContinue(); dismiss() }
                        .buttonStyle(.borderedProminent)
                    Button("按选择的时间修正") { onAdjust(start, end); dismiss() }
                    Button("放弃本次记录", role: .destructive) { onDiscard(); dismiss() }
                }
            }
            .navigationTitle("恢复计时")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func format(date: Date) -> String {
        let f = DateFormatter()
        f.dateStyle = .medium
        f.timeStyle = .short
        return f.string(from: date)
    }

    private func format(duration: TimeInterval) -> String {
        let h = Int(duration) / 3600
        let m = Int(duration) % 3600 / 60
        let s = Int(duration) % 60
        return h > 0 ? String(format: "%02d:%02d:%02d", h, m, s) : String(format: "%02d:%02d", m, s)
    }
}

