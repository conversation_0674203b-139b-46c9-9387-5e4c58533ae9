//
//  TimeTrackerView.swift
//  TimeScale
//
//  Created by MeeYou on 2025/8/25.
//

import SwiftUI
import SwiftData

struct TimeTrackerView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(filter: #Predicate<TimeCategory> { !$0.archived }, sort: \TimeCategory.order)
    private var categories: [TimeCategory]
    @Query(filter: #Predicate<TimeEntry> { $0.isRunning })
    private var runningEntries: [TimeEntry]

    @State private var showingAddCategory = false

    @Environment(\.scenePhase) private var scenePhase

    var body: some View {
        NavigationView {
            VStack {
                // 正在运行的计时器
                if let runningEntry = runningEntries.first {
                    RunningTimerCard(entry: runningEntry)
                        .padding()
                }

                if let recovery = TimeTrackingManager.shared.pendingRecoveryEntry,
                   let message = TimeTrackingManager.shared.abnormalMessage {
                    // 新增 - 异常恢复入口
                    RecoveryBanner(entry: recovery, message: message)
                        .padding(.horizontal)
                }

                if categories.isEmpty {
                    // 空状态
                    VStack(spacing: 20) {
                        Image(systemName: "timer")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)

                        Text(LocalizedStringKey("还没有时间分类"))
                            .font(.title2)
                            .fontWeight(.medium)

                        Text(LocalizedStringKey("创建时间分类，开始记录你的时间使用"))
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        Button(LocalizedStringKey("创建时间分类")) {
                            showingAddCategory = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 分类列表
                    List {
                        ForEach(categories) { category in
                            TimeCategoryRowView(category: category)
                        }
                    }
                }
            }
            .navigationTitle("时间记录")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    NavigationLink(destination: WeeklyReportView()) {
                        Image(systemName: "chart.bar")
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddCategory = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddCategory) {
                AddTimeCategoryView()
            }
            // 修改 - 监听场景生命周期，记录后台/恢复
            .onChange(of: scenePhase) { _, newPhase in
                TimeTrackingManager.shared.handleScenePhaseChange(newPhase, context: modelContext)
            }
            // 修改 - 首次出现时进行一次恢复检查
            .task {
                TimeTrackingManager.shared.checkForRecoveryOnLaunch(context: modelContext)
            }
        }
    }
}

struct RecoveryBanner: View {
    @Environment(\.modelContext) private var modelContext
    let entry: TimeEntry
    let message: String
    @State private var showSheet = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill").foregroundStyle(.orange)
                Text(message).font(.subheadline)
                Spacer()
                Button("处理…") { showSheet = true }
            }
        }
        .padding(12)
        .background(Color.orange.opacity(0.12))
        .cornerRadius(10)
        .sheet(isPresented: $showSheet) {
            TimerRecoverySheet(
                entry: entry,
                message: message,
                onContinue: {
                    // 继续计时：不做改变，仅清除恢复状态
                    TimeTrackingManager.shared.pendingRecoveryEntry = nil
                    TimeTrackingManager.shared.abnormalMessage = nil
                },
                onAdjust: { start, end in
                    // 将条目调整为指定开始/结束并结束计时
                    entry.start = start
                    entry.end = end
                    entry.durationSec = max(0, end.timeIntervalSince(start))
                    entry.isRunning = false
                    try? modelContext.save()
                    TimeTrackingManager.shared.pendingRecoveryEntry = nil
                    TimeTrackingManager.shared.abnormalMessage = nil
                },
                onDiscard: {
                    // 删除本次记录
                    modelContext.delete(entry)
                    try? modelContext.save()
                    TimeTrackingManager.shared.pendingRecoveryEntry = nil
                    TimeTrackingManager.shared.abnormalMessage = nil
                }
            )
        }
    }
}

// MARK: - 正在运行的计时器卡片
struct RunningTimerCard: View {
    let entry: TimeEntry
    @Environment(\.modelContext) private var modelContext

    // 修改 - 使用 TimelineView 周期性刷新，避免 Timer 管理复杂性
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Circle()
                    .fill(Color(hex: entry.category?.colorHex ?? "#007AFF"))
                    .frame(width: 12, height: 12)

                Text(entry.category?.name ?? NSLocalizedString("未分类", comment: ""))
                    .font(.headline)

                Spacer()

                Button(LocalizedStringKey("停止")) {
                    stopTimer()
                }
                .buttonStyle(.bordered)
            }

            TimelineView(.periodic(from: .now, by: 1)) { _ in
                Text(formatDuration(entry.currentDuration))
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .monospacedDigit()
            }
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(12)
    }

    private func stopTimer() {
        // 停止后立即持久化，确保UI与数据一致
        entry.stopTimer()
        try? modelContext.save()
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - 时间分类行视图
struct TimeCategoryRowView: View {
    let category: TimeCategory
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        HStack {
            Circle()
                .fill(Color(hex: category.colorHex))
                .frame(width: 12, height: 12)

            Text(category.name)
                .font(.headline)

            Spacer()

            Button("开始") {
                startTimer()
            }
            .buttonStyle(.borderedProminent)
        }
            .padding(.vertical, 4)

        .padding(.vertical, 4)
    }

    private func startTimer() {
        // 停止其他正在运行的计时器
        let runningEntries = try? modelContext.fetch(FetchDescriptor<TimeEntry>(
            predicate: #Predicate { $0.isRunning }
        ))

        runningEntries?.forEach { $0.stopTimer() }

        // 开始新的计时器
        let newEntry = TimeEntry(category: category)
        modelContext.insert(newEntry)
        try? modelContext.save()
        // 可选：安排一个轻提示本地通知，提醒用户在后台有计时
        Task { await TimeTrackingManager.shared.scheduleRunningIndicatorNotification(for: newEntry) }
    }
}

// MARK: - 临时占位视图
struct AddTimeCategoryView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var selectedColor = "#007AFF"

    private let availableColors = [
        "#007AFF", "#FF3B30", "#FF9500", "#FFCC00",
        "#34C759", "#5AC8FA", "#AF52DE", "#FF2D92",
        "#A2845E", "#8E8E93"
    ]

    var body: some View {
        NavigationView {
            Form {
                Section("基本信息") {
                    TextField("分类名称", text: $name)

                    VStack(alignment: .leading) {
                        Text("颜色")
                            .font(.headline)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 10) {
                            ForEach(availableColors, id: \.self) { color in
                                Circle()
                                    .fill(Color(hex: color))
                                    .frame(width: 30, height: 30)
                                    .overlay(
                                        Circle()
                                            .stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 2)
                                    )
                                    .onTapGesture {
                                        selectedColor = color
                                    }
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("新建分类")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveCategory()
                    }
                    .disabled(name.isEmpty)
                }
            }
        }
    }

    private func saveCategory() {
        let category = TimeCategory(name: name, colorHex: selectedColor)
        modelContext.insert(category)
        try? modelContext.save()

        dismiss()
    }
}

#Preview {
    TimeTrackerView()
        .modelContainer(for: [TimeCategory.self, TimeEntry.self], inMemory: true)
}
