//
//  AddHabitView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData

#if canImport(AlertToast)
import AlertToast
#endif

struct AddHabitView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var selectedColor = "#007AFF"
    @State private var scheduleRule = ScheduleRule.daily
    @State private var customWeekdays: Set<Int> = []
    @State private var reminderTimes: [DateComponents] = []
    @State private var showingReminderPicker = false
    // 新增 - Toast 状态
    @State private var showToast = false
    @State private var toastTitle = ""
    @State private var toastSub: String? = nil
    @State private var toastType: AppToastType = .info


    // 新增 - 通知权限相关状态
    @State private var showingPermissionAlert = false
    @EnvironmentObject private var notificationManager: NotificationManager

    private let availableColors = [
        "#007AFF", "#FF3B30", "#FF9500", "#FFCC00",
        "#34C759", "#5AC8FA", "#AF52DE", "#FF2D92",
        "#A2845E", "#8E8E93"
    ]

    private let weekdays = [
        (1, "周一"), (2, "周二"), (3, "周三"), (4, "周四"),
        (5, "周五"), (6, "周六"), (7, "周日")
    ]

    var body: some View {
        NavigationView {
            Form {
                Section("基本信息") {
                    TextField("习惯名称", text: $name)

                    // 颜色选择
                    VStack(alignment: .leading) {
                        Text("颜色")
                            .font(.headline)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 10) {
                            ForEach(availableColors, id: \.self) { color in
                                Circle()
                                    .fill(Color(hex: color))
                                    .frame(width: 30, height: 30)
                                    .overlay(
                                        Circle()
                                            .stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 2)
                                    )
                                    .onTapGesture {
                                        selectedColor = color
                                    }
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }

                Section("重复规则") {
                    Picker("重复规则", selection: $scheduleRule) {
                        ForEach(ScheduleRule.allCases, id: \.self) { rule in
                            Text(rule.displayName).tag(rule)
                        }
                    }
                    .pickerStyle(.segmented)

                    if scheduleRule == .custom {
                        VStack(alignment: .leading) {
                            Text("选择重复的日期")
                                .font(.headline)

                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                                ForEach(weekdays, id: \.0) { day, name in
                                    Button {
                                        if customWeekdays.contains(day) {
                                            customWeekdays.remove(day)
                                        } else {
                                            customWeekdays.insert(day)
                                        }
                                    } label: {
                                        Text(name)
                                            .font(.caption)
                                            .foregroundColor(customWeekdays.contains(day) ? .white : .primary)
                                            .frame(width: 40, height: 30)
                                            .background(customWeekdays.contains(day) ? Color.blue : Color.gray.opacity(0.2))
                                            .cornerRadius(6)
                                    }
                                    .buttonStyle(.plain)
                                }
                            }
                        }
                        .padding(.vertical, 8)
                    }
                }

                Section("提醒设置") {
                    if reminderTimes.isEmpty {
                        Button("添加提醒时间") {
                            // 修改 - 添加通知权限检查
                            checkNotificationPermissionAndAddReminder()
                        }
                    } else {
                        ForEach(Array(reminderTimes.enumerated()), id: \.offset) { index, time in
                            HStack {
                                Text(formatTime(time))
                                Spacer()
                                Button("删除") {
                                    reminderTimes.remove(at: index)
                                }
                                .foregroundColor(.red)
                            }
                        }

                        if reminderTimes.count < 3 {
                            Button("添加提醒时间") {
                                // 修改 - 添加通知权限检查
                                checkNotificationPermissionAndAddReminder()
                            }
                        }
                    }
                }
            }
            .navigationTitle("新建习惯")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveHabit()
                    }
                    .disabled(name.isEmpty || (scheduleRule == .custom && customWeekdays.isEmpty))
                }
            }
            .sheet(isPresented: $showingReminderPicker) {
                ReminderTimePickerView { time in
                    reminderTimes.append(time)
                }
            }
            // 修改 - 使用Toast替代通知权限阻断弹窗
            .onChange(of: showingPermissionAlert) { _, newValue in
                if newValue {
                    toastType = .info
                    toastTitle = "需要通知权限"
                    toastSub = "请在系统设置中允许TimeScale发送通知"
                    showToast = true
                    showingPermissionAlert = false
                }
            }
            .appToast(isPresenting: $showToast, type: toastType, title: toastTitle, subTitle: toastSub)
        }
    }

    private func saveHabit() {
        let habit = Habit(name: name, colorHex: selectedColor, scheduleRule: scheduleRule)

        if scheduleRule == .custom {
            habit.customWeekdays = Array(customWeekdays)
        }

        habit.reminderTimes = reminderTimes

        modelContext.insert(habit)
        try? modelContext.save()

        // 修改 - 设置通知提醒时检查权限
        if !reminderTimes.isEmpty {
            Task {
                await NotificationManager.shared.scheduleHabitReminders(for: habit)
            }
        }

        dismiss()
    }

    // 新增 - 检查通知权限并添加提醒
    private func checkNotificationPermissionAndAddReminder() {
        Task {
            // 更新权限状态
            await notificationManager.updateAuthorizationStatus()

            await MainActor.run {
                switch notificationManager.authorizationStatus {
                case .authorized:
                    // 已授权，直接显示时间选择器
                    showingReminderPicker = true

                case .notDetermined:
                    // 未确定，请求权限
                    Task {
                        let granted = await notificationManager.requestAuthorization()
                        await MainActor.run {
                            if granted {
                                showingReminderPicker = true
                            } else {
                                showingPermissionAlert = true
                            }
                        }
                    }

                case .denied, .provisional, .ephemeral:
                    // 被拒绝或其他状态，显示设置提示
                    showingPermissionAlert = true

                @unknown default:
                    showingPermissionAlert = true
                }
            }
        }
    }

    private func formatTime(_ time: DateComponents) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short

        let calendar = Calendar.current
        let date = calendar.date(from: time) ?? Date()
        return formatter.string(from: date)
    }
}

// MARK: - 提醒时间选择器
struct ReminderTimePickerView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTime = Date()

    let onTimeSelected: (DateComponents) -> Void

    var body: some View {
        NavigationView {
            VStack {
                DatePicker("选择时间", selection: $selectedTime, displayedComponents: .hourAndMinute)
                    .datePickerStyle(.wheel)
                    .labelsHidden()

                Spacer()
            }
            .padding()
            .navigationTitle("设置提醒时间")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        let components = Calendar.current.dateComponents([.hour, .minute], from: selectedTime)
                        onTimeSelected(components)
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AddHabitView()
        .modelContainer(for: [Habit.self], inMemory: true)
}
