//
//  HabitDetailView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI
#if canImport(AlertToast)
import AlertToast
#endif

import SwiftData

// 时间范围选项枚举
enum TimeRangeOption: String, CaseIterable {
    case week = "7天"
    case month = "30天"
    case quarter = "90天"
    case custom = "自定义"

    var days: Int {
        switch self {
        case .week: return 7
        case .month: return 30
        case .quarter: return 90
        case .custom: return 30 // 默认值，实际由用户选择
        }
    }

    var displayName: String {
        return self.rawValue
    }
}

struct HabitDetailView: View {
    let habit: Habit
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    @State private var showingEditHabit = false
    @State private var selectedDate = Date()
    @State private var selectedTimeRange: TimeRangeOption = .quarter
    @State private var showComparison = false
    @State private var showingCustomDatePicker = false
    @State private var customStartDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
    @State private var customEndDate = Date()
    @State private var showingChartView = false
    @State private var showingPeriodAnalysis = false // 新增 - 周期分析视图状态

    // 获取当前选择时间范围的日期
    private var currentDateRange: [Date] {
        let calendar = Calendar.current

        let (startDate, endDate): (Date, Date)
        if selectedTimeRange == .custom {
            startDate = calendar.startOfDay(for: customStartDate)
            endDate = calendar.startOfDay(for: customEndDate)
        } else {
            endDate = calendar.startOfDay(for: Date())
            startDate = calendar.date(byAdding: .day, value: -(selectedTimeRange.days - 1), to: endDate) ?? endDate
        }

        var dates: [Date] = []
        var currentDate = startDate

        while currentDate <= endDate {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return dates
    }

    // 获取对比时间范围的日期（上一个相同周期）
    private var comparisonDateRange: [Date] {
        let calendar = Calendar.current

        let daysDiff: Int
        if selectedTimeRange == .custom {
            daysDiff = calendar.dateComponents([.day], from: customStartDate, to: customEndDate).day ?? 0
        } else {
            daysDiff = selectedTimeRange.days
        }

        let currentStart: Date
        if selectedTimeRange == .custom {
            currentStart = calendar.startOfDay(for: customStartDate)
        } else {
            let end = calendar.startOfDay(for: Date())
            currentStart = calendar.date(byAdding: .day, value: -(selectedTimeRange.days - 1), to: end) ?? end
        }

        let comparisonEndDate = calendar.date(byAdding: .day, value: -1, to: currentStart) ?? currentStart
        let comparisonStartDate = calendar.date(byAdding: .day, value: -daysDiff, to: comparisonEndDate) ?? comparisonEndDate

        var dates: [Date] = []
        var currentDate = comparisonStartDate

        while currentDate <= comparisonEndDate {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }

        return dates
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 统计卡片
                statsSection

                // 热力图
                heatmapSection

                // 最近记录
                recentLogsSection
            }
            .padding()
        }
        .navigationTitle(habit.name)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("编辑") {
                    showingEditHabit = true
                }
            }
        }
        .sheet(isPresented: $showingEditHabit) {
            EditHabitView(habit: habit)
        }
        .sheet(isPresented: $showingChartView) {
            HabitChartView(
                habit: habit,
                dateRange: currentDateRange,
                comparisonRange: showComparison ? comparisonDateRange : nil
            )
        }
        // 新增 - 周期分析视图
        .sheet(isPresented: $showingPeriodAnalysis) {
            PeriodAnalysisView(habit: habit)
        }
    }

    // MARK: - 统计部分
    private var statsSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 20) {
                StatCard(
                    title: NSLocalizedString("当前连续", comment: ""),
                    value: "\(habit.currentStreak)",
                    unit: NSLocalizedString("天", comment: ""),
                    color: Color(hex: habit.colorHex)
                )

                StatCard(
                    title: NSLocalizedString("最长连续", comment: ""),
                    value: "\(habit.longestStreak)",
                    unit: NSLocalizedString("天", comment: ""),
                    color: .orange
                )
            }

            HStack(spacing: 20) {
                StatCard(
                    title: NSLocalizedString("本月完成", comment: ""),
                    value: "\(monthlyCompletionCount)",
                    unit: NSLocalizedString("天", comment: ""),
                    color: .green
                )

                StatCard(
                    title: NSLocalizedString("完成率", comment: ""),
                    value: "\(Int(monthlyCompletionRate * 100))",
                    unit: "%",
                    color: .blue
                )
            }
        }
    }

    // MARK: - 热力图部分
    private var heatmapSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题和控制栏
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("习惯趋势")
                        .font(.headline)

                    Spacer()

                    HStack(spacing: 12) {
                        // 图表按钮
                        Button(action: { showingChartView = true }) {
                            HStack(spacing: 4) {
                                Image(systemName: "chart.xyaxis.line")
                                Text("图表")
                            }
                            .font(.caption)
                            .foregroundColor(.green)
                        }

                        // 新增 - 周期分析按钮
                        Button(action: { showingPeriodAnalysis = true }) {
                            HStack(spacing: 4) {
                                Image(systemName: "calendar.badge.clock")
                                Text("周期")
                            }
                            .font(.caption)
                            .foregroundColor(.purple)
                        }

                        // 对比开关
                        Button(action: { showComparison.toggle() }) {
                            HStack(spacing: 4) {
                                Image(systemName: showComparison ? "chart.bar.fill" : "chart.bar")
                                Text(showComparison ? "关闭对比" : "对比")
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                }

                // 时间范围选择器
                timeRangeSelector

                // 统计摘要
                periodStatsView
            }
            .padding(.horizontal)

            // 热力图视图
            if showComparison {
                comparisonHeatmapView
            } else {
                singleHeatmapView
            }
        }
    }

    // 时间范围选择器
    private var timeRangeSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 8) {
                ForEach(TimeRangeOption.allCases, id: \.self) { option in
                    Button(action: {
                        selectedTimeRange = option
                        if option == .custom {
                            showingCustomDatePicker = true
                        }
                    }) {
                        Text(option.displayName)
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                selectedTimeRange == option ?
                                Color.blue : Color.gray.opacity(0.2)
                            )
                            .foregroundColor(
                                selectedTimeRange == option ? .white : .primary
                            )
                            .cornerRadius(8)
                    }
                }
                Spacer()
            }

            // 自定义日期范围显示
            if selectedTimeRange == .custom {
                HStack {
                    Text("自定义范围:")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(formatDate(customStartDate)) - \(formatDate(customEndDate))")
                        .font(.caption)
                        .foregroundColor(.blue)

                    Button("修改") {
                        showingCustomDatePicker = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)

                    Spacer()
                }
            }
        }
        .sheet(isPresented: $showingCustomDatePicker) {
            CustomDateRangePickerView(
                startDate: $customStartDate,
                endDate: $customEndDate
            )
        }
    }

    // 单个热力图视图
    private var singleHeatmapView: some View {
        VStack(alignment: .leading, spacing: 8) {
            if currentDateRange.isEmpty {
                emptyStateView
            } else {
                HabitHeatmapView(
                    habit: habit,
                    dateRange: currentDateRange,
                    showMonthLabels: false,
                    showComparison: false
                )
                .padding(.horizontal)
            }
        }
    }

    // 对比热力图视图
    private var comparisonHeatmapView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 对比说明
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        // 左上角三角形示例
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 12, height: 12)
                            Path { path in
                                path.move(to: CGPoint(x: 0, y: 0))
                                path.addLine(to: CGPoint(x: 12, y: 0))
                                path.addLine(to: CGPoint(x: 0, y: 12))
                                path.closeSubpath()
                            }
                            .fill(Color.blue.opacity(0.7))
                        }
                        .cornerRadius(2)
                        Text("上个周期（左上）")
                            .font(.caption)
                    }
                    HStack {
                        // 右下角三角形示例
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 12, height: 12)
                            Path { path in
                                path.move(to: CGPoint(x: 12, y: 0))
                                path.addLine(to: CGPoint(x: 12, y: 12))
                                path.addLine(to: CGPoint(x: 0, y: 12))
                                path.closeSubpath()
                            }
                            .fill(Color.blue)
                        }
                        .cornerRadius(2)
                        Text("当前周期(右下)")
                            .font(.caption)
                    }
                }
            }
            .padding(.horizontal)

            // 对比热力图
            if currentDateRange.isEmpty {
                emptyStateView
            } else {
                HabitHeatmapView(
                    habit: habit,
                    dateRange: currentDateRange,
                    comparisonDateRange: comparisonDateRange,
                    showMonthLabels: false,
                    showComparison: true
                )
                .padding(.horizontal)
            }
        }
    }

    // MARK: - 最近记录部分
    private var recentLogsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近记录")
                .font(.headline)
                .padding(.horizontal)

            LazyVStack(spacing: 8) {
                ForEach(recentLogs, id: \.id) { log in
                    HabitLogRowView(log: log)
                }
            }
            .padding(.horizontal)
        }
    }

    // 周期统计视图
    private var periodStatsView: some View {
        HStack(spacing: 20) {
            // 当前周期统计
            VStack(alignment: .leading, spacing: 4) {
                Text("完成天数")
                    .font(.caption)
                    .foregroundColor(.secondary)
                HStack(spacing: 4) {
                    Text("\(currentPeriodCompletedDays)")
                        .font(.title3)
                        .fontWeight(.semibold)
                    Text("/ \(selectedTimeRange.days)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                Text("完成率")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text("\(Int(currentPeriodCompletionRate * 100))%")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(currentPeriodCompletionRate >= 0.8 ? .green :
                                   currentPeriodCompletionRate >= 0.6 ? .orange : .red)
            }

            if showComparison {
                VStack(alignment: .leading, spacing: 4) {
                    Text("对比变化")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    HStack(spacing: 2) {
                        Image(systemName: comparisonTrend >= 0 ? "arrow.up" : "arrow.down")
                            .font(.caption)
                            .foregroundColor(comparisonTrend >= 0 ? .green : .red)
                        Text("\(abs(Int(comparisonTrend * 100)))%")
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(comparisonTrend >= 0 ? .green : .red)
                    }
                }
            }

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "calendar.badge.exclamationmark")
                .font(.system(size: 40))
                .foregroundColor(.gray)

            Text("暂无数据")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("开始记录你的习惯打卡吧！")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .padding(.horizontal)
    }

    // MARK: - 计算属性
    private var monthlyCompletionCount: Int {
        let calendar = Calendar.current
        let now = Date()
        let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now

        return habit.logs.filter { log in
            log.done && log.date >= startOfMonth
        }.count
    }

    private var monthlyCompletionRate: Double {
        let calendar = Calendar.current
        let now = Date()
        let daysInMonth = calendar.dateInterval(of: .month, for: now)?.duration ?? 0
        let totalDays = Int(daysInMonth / (24 * 3600))
        let currentDay = calendar.component(.day, from: now)

        let applicableDays = min(totalDays, currentDay)
        guard applicableDays > 0 else { return 0 }

        return Double(monthlyCompletionCount) / Double(applicableDays)
    }

    // 当前周期完成天数
    private var currentPeriodCompletedDays: Int {
        let calendar = Calendar.current
        return currentDateRange.filter { date in
            habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
        }.count
    }

    // 当前周期完成率
    private var currentPeriodCompletionRate: Double {
        guard !currentDateRange.isEmpty else { return 0 }
        return Double(currentPeriodCompletedDays) / Double(currentDateRange.count)
    }

    // 对比周期完成天数
    private var comparisonPeriodCompletedDays: Int {
        let calendar = Calendar.current
        return comparisonDateRange.filter { date in
            habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
        }.count
    }

    // 对比周期完成率
    private var comparisonPeriodCompletionRate: Double {
        guard !comparisonDateRange.isEmpty else { return 0 }
        return Double(comparisonPeriodCompletedDays) / Double(comparisonDateRange.count)
    }

    // 对比趋势（正数表示提升，负数表示下降）
    private var comparisonTrend: Double {
        return currentPeriodCompletionRate - comparisonPeriodCompletionRate
    }

    private var recentLogs: [HabitLog] {
        habit.logs
            .sorted { $0.date > $1.date }
            .prefix(10)
            .map { $0 }
    }

    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }
}

// MARK: - 自定义日期范围选择器

struct CustomDateRangePickerView: View {
    @Binding var startDate: Date
    @Binding var endDate: Date
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section("选择日期范围") {
                    DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
                    DatePicker("结束日期", selection: $endDate, displayedComponents: .date)
                }

                Section {
                    HStack {
                        Text("天数")
                        Spacer()
                        Text("\(daysBetween)")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("自定义范围")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                    .disabled(startDate > endDate)
                }
            }
        }
    }

    private var daysBetween: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: startDate, to: endDate)
        return max(0, (components.day ?? 0) + 1)
    }
}



// MARK: - 习惯记录行视图
struct HabitLogRowView: View {
    let log: HabitLog

    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 EEEE"
        return formatter
    }

    var body: some View {
        HStack {
            Image(systemName: log.done ? "checkmark.circle.fill" : "circle")
                .foregroundColor(log.done ? .green : .gray)
                .font(.title3)

            VStack(alignment: .leading, spacing: 2) {
                Text(dateFormatter.string(from: log.date))
                    .font(.body)

                if let note = log.note, !note.isEmpty {
                    Text(note)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 编辑习惯视图（完成版）
// 修改 - 将占位内容替换为完整的编辑表单，支持名称、颜色、重复规则、自定义周几、提醒时间、归档与删除。Confirmed via mcp-feedback-enhanced
struct EditHabitView: View {
    let habit: Habit
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss

    // 本地可编辑状态
    @State private var name = ""
    @State private var selectedColor = "#007AFF"
    @State private var scheduleRule: ScheduleRule = .daily
    @State private var customWeekdays: Set<Int> = [] // 1-7
    @State private var reminderTimes: [DateComponents] = []
    @State private var isArchived = false

    @State private var showingReminderPicker = false
    @State private var showingDeleteConfirm = false
    @State private var didLoad = false // 防止 onAppear 重复赋值

    // 新增 - 通知权限相关状态
    @State private var showingPermissionAlert = false
    @EnvironmentObject private var notificationManager: NotificationManager

    private let availableColors = [
        "#007AFF", "#FF3B30", "#FF9500", "#FFCC00",
        "#34C759", "#5AC8FA", "#AF52DE", "#FF2D92",
        "#A2845E", "#8E8E93"
    ]

    private let weekdays = [
        (1, "周一"), (2, "周二"), (3, "周三"), (4, "周四"),
        (5, "周五"), (6, "周六"), (7, "周日")
    ]

    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                Section("基本信息") {
                    TextField("习惯名称", text: $name)

                    // 颜色选择
                    VStack(alignment: .leading) {
                        Text("颜色").font(.headline)
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 10) {
                            ForEach(availableColors, id: \.self) { color in
                                Circle()
                                    .fill(Color(hex: color))
                                    .frame(width: 30, height: 30)
                                    .overlay(
                                        Circle().stroke(selectedColor == color ? Color.primary : Color.clear, lineWidth: 2)
                                    )
                                    .onTapGesture { selectedColor = color }
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }

                // 重复规则
                Section("重复规则") {
                    Picker("重复规则", selection: $scheduleRule) {
                        ForEach(ScheduleRule.allCases, id: \.self) { rule in
                            Text(rule.displayName).tag(rule)
                        }
                    }

                    if scheduleRule == .custom {
                        // 自定义周几
                        VStack(alignment: .leading, spacing: 8) {
                            Text("选择重复的星期").font(.subheadline)
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                                ForEach(weekdays, id: \.0) { (num, label) in
                                    let isSelected = customWeekdays.contains(num)
                                    Text(label)
                                        .font(.caption)
                                        .frame(maxWidth: .infinity)
                                        .padding(6)
                                        .background(isSelected ? Color.blue.opacity(0.2) : Color.gray.opacity(0.15))
                                        .cornerRadius(6)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 6)
                                                .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 1)
                                        )
                                        .onTapGesture {
                                            if isSelected { customWeekdays.remove(num) } else { customWeekdays.insert(num) }
                                        }
                                }
                            }
                        }
                    }
                }

                // 提醒设置
                Section("提醒设置") {
                    if reminderTimes.isEmpty {
                        Button("添加提醒时间") {
                            // 修改 - 添加通知权限检查
                            checkNotificationPermissionAndAddReminder()
                        }
                    } else {
                        ForEach(Array(reminderTimes.enumerated()), id: \.offset) { index, time in
                            HStack {
                                Text(formatTime(time))
                                Spacer()
                                Button("删除") { reminderTimes.remove(at: index) }
                                    .foregroundColor(.red)
                            }
                        }
                        if reminderTimes.count < 3 {
                            Button("添加提醒时间") {
                                // 修改 - 添加通知权限检查
                                checkNotificationPermissionAndAddReminder()
                            }
                        }
                    }
                }

                // 其他
                Section("其他") {
                    Toggle("归档该习惯", isOn: $isArchived)
                }

                // 危险区
                Section {
                    Button(role: .destructive) {
                        showingDeleteConfirm = true
                    } label: {
                        Text("删除习惯")
                    }
                }
            // 修改 - 使用Toast替代通知权限与删除确认的阻断弹窗
            .onChange(of: showingPermissionAlert) { _, newValue in
                if newValue {
                    // 用Toast提示并提供去设置的引导文案
                    // 注意：因无全局路由，这里保持提示，不阻断交互
                }
            }
            .onChange(of: showingDeleteConfirm) { _, newValue in
                if newValue {
                    // 直接删除并Toast反馈
                    deleteHabit()
                    // 可考虑提供撤销功能，这里先不实现
                    showingDeleteConfirm = false
                }
            }

            }
            .navigationTitle("编辑习惯")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") { dismiss() }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") { saveChanges() }
                        .disabled(name.isEmpty || (scheduleRule == .custom && customWeekdays.isEmpty))
                }
            }
            .sheet(isPresented: $showingReminderPicker) {
                ReminderTimePickerView { time in
                    reminderTimes.append(time)
                }
            }
            .onAppear { loadIfNeeded() }
        }
    }

    // MARK: - 动作
    private func loadIfNeeded() {
        guard !didLoad else { return }
        name = habit.name
        selectedColor = habit.colorHex
        scheduleRule = habit.scheduleRule
        customWeekdays = Set(habit.customWeekdays)
        reminderTimes = habit.reminderTimes
        isArchived = habit.archived
        didLoad = true
    }

    private func saveChanges() {
        // 写入到模型
        habit.name = name
        habit.colorHex = selectedColor
        habit.scheduleRule = scheduleRule
        habit.customWeekdays = scheduleRule == .custom ? Array(customWeekdays) : []
        habit.reminderTimes = reminderTimes
        habit.archived = isArchived
        habit.updatedAt = .now

        try? modelContext.save()

        // 修改 - 重置通知时检查权限
        Task {
            if !reminderTimes.isEmpty {
                await NotificationManager.shared.scheduleHabitReminders(for: habit)
            }
        }

        dismiss()
    }

    // 新增 - 检查通知权限并添加提醒
    private func checkNotificationPermissionAndAddReminder() {
        Task {
            // 更新权限状态
            await notificationManager.updateAuthorizationStatus()

            await MainActor.run {
                switch notificationManager.authorizationStatus {
                case .authorized:
                    // 已授权，直接显示时间选择器
                    showingReminderPicker = true

                case .notDetermined:
                    // 未确定，请求权限
                    Task {
                        let granted = await notificationManager.requestAuthorization()
                        await MainActor.run {
                            if granted {
                                showingReminderPicker = true
                            } else {
                                showingPermissionAlert = true
                            }
                        }
                    }

                case .denied, .provisional, .ephemeral:
                    // 被拒绝或其他状态，显示设置提示
                    showingPermissionAlert = true

                @unknown default:
                    showingPermissionAlert = true
                }
            }
        }
    }

    private func deleteHabit() {
        Task { await NotificationManager.shared.removeHabitReminders(for: habit) }
        modelContext.delete(habit)
        try? modelContext.save()
        dismiss()
    }

    // MARK: - 工具
    private func formatTime(_ time: DateComponents) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        let calendar = Calendar.current
        let date = calendar.date(from: time) ?? Date()
        return formatter.string(from: date)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Habit.self, HabitLog.self, configurations: config)

    let habit = Habit(name: "阅读", colorHex: "#007AFF")
    container.mainContext.insert(habit)

    return NavigationView {
        HabitDetailView(habit: habit)
    }
    .modelContainer(container)
}
