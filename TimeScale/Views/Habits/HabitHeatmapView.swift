//
//  HabitHeatmapView.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/25.
//

import SwiftUI

struct HabitHeatmapView: View {
    let habit: Habit
    let dateRange: [Date]
    let comparisonDateRange: [Date]?
    let showMonthLabels: Bool
    let showComparison: Bool
    let cellScale: CGFloat // 新增 - 单元格缩放比例，便于统一缩小网格尺寸

    @State private var selectedDate: Date?

    private let columns = 7 // 一周7天
    private let cellSpacing: CGFloat = 2

    // 动态计算单元格大小
    private var cellSize: CGFloat {
        let totalDays = dateRange.count
        let base: CGFloat
        if totalDays > 90 {
            base = 8 // 大于90天时减小单元格
        } else if totalDays > 60 {
            base = 10
        } else {
            base = 12
        }
        return max(6, base * max(0.6, cellScale)) // 限制最小尺寸，避免过小
    }

    // 动态计算单元格大小和间距
    private var dynamicCellSpacing: CGFloat {
        let totalDays = dateRange.count
        let base: C<PERSON><PERSON>loat
        if totalDays > 90 {
            base = 1 // 大于90天时减少间距
        } else if totalDays > 60 {
            base = 1.5
        } else {
            base = cellSpacing
        }
        return max(0.5, base * max(0.6, cellScale))
    }

    // 创建网格列配置
    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: dynamicCellSpacing), count: columns)
    }

    init(habit: Habit, dateRange: [Date], comparisonDateRange: [Date]? = nil, showMonthLabels: Bool = true, showComparison: Bool = false, cellScale: CGFloat = 0.5) {
        self.habit = habit
        self.dateRange = dateRange
        self.comparisonDateRange = comparisonDateRange
        self.showMonthLabels = showMonthLabels
        self.showComparison = showComparison
        self.cellScale = cellScale
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 月份标签（可选）
            if showMonthLabels {
                monthLabels
            }

            // 热力图网格
            if showComparison && comparisonDateRange != nil {
                comparisonHeatmapGrid
            } else {
                singleHeatmapGrid
            }

            // 图例
            legend
        }
    }
    
    // MARK: - 月份标签
    private var monthLabels: some View {
        HStack {
            // 修改 - 使用数组索引作为唯一ID，避免position重复导致的ID冲突
            ForEach(Array(monthPositions.enumerated()), id: \.offset) { index, monthPos in
                Text(monthPos.name)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .frame(width: cellSize * 4.3, alignment: .leading)
                    .offset(x: CGFloat(monthPos.position) * (cellSize + cellSpacing))
            }
            Spacer()
        }
        .frame(height: 16)
    }
    
    // MARK: - 单个热力图网格
    private var singleHeatmapGrid: some View {
        LazyVGrid(columns: gridColumns, spacing: dynamicCellSpacing) {
            ForEach(Array(dateRange.enumerated()), id: \.offset) { index, date in
                let intensity = getIntensity(for: date, in: dateRange)

                Rectangle()
                    .fill(getColor(for: intensity))
                    .aspectRatio(1, contentMode: .fit)
                    .cornerRadius(2)
                    .onTapGesture {
                        selectedDate = date
                    }
                    .overlay(
                        RoundedRectangle(cornerRadius: 2)
                            .stroke(selectedDate == date ? Color.primary : Color.clear, lineWidth: 1)
                    )
            }

            // 填充空白单元格以完成最后一周
            let remainingCells = (columns - (dateRange.count % columns)) % columns
            ForEach(0..<remainingCells, id: \.self) { emptyIndex in
                Rectangle()
                    .fill(Color.clear)
                    .aspectRatio(1, contentMode: .fit)
                    .id("empty_\(emptyIndex)")
            }
        }
    }

    // MARK: - 对比热力图网格
    private var comparisonHeatmapGrid: some View {
        LazyVGrid(columns: gridColumns, spacing: dynamicCellSpacing) {
            ForEach(Array(dateRange.enumerated()), id: \.offset) { index, currentDate in
                let comparisonDate = comparisonDateRange?[safe: index]

                // 对比模式：左上角显示上一周期，右下角显示当前周期（从右上到左下的分割线）
                GeometryReader { geometry in
                    let size = min(geometry.size.width, geometry.size.height)

                    ZStack {
                        Rectangle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: size, height: size)
                            .cornerRadius(2)

                        // 上一周期（左上角三角形）
                        if let compDate = comparisonDate {
                            let compIntensity = getIntensity(for: compDate, in: comparisonDateRange ?? [])
                            Path { path in
                                path.move(to: CGPoint(x: 0, y: 0))
                                path.addLine(to: CGPoint(x: size, y: 0))
                                path.addLine(to: CGPoint(x: 0, y: size))
                                path.closeSubpath()
                            }
                            .fill(getColor(for: compIntensity).opacity(0.7))
                        }

                        // 当前周期（右下角三角形）
                        let currentIntensity = getIntensity(for: currentDate, in: dateRange)
                        Path { path in
                            path.move(to: CGPoint(x: size, y: 0))
                            path.addLine(to: CGPoint(x: size, y: size))
                            path.addLine(to: CGPoint(x: 0, y: size))
                            path.closeSubpath()
                        }
                        .fill(getColor(for: currentIntensity))
                    }
                }
                .aspectRatio(1, contentMode: .fit)
                .onTapGesture {
                    selectedDate = currentDate
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 2)
                        .stroke(selectedDate == currentDate ? Color.primary : Color.clear, lineWidth: 1)
                )
            }

            // 填充空白单元格以完成最后一周
            let remainingCells = (columns - (dateRange.count % columns)) % columns
            ForEach(0..<remainingCells, id: \.self) { emptyIndex in
                Rectangle()
                    .fill(Color.clear)
                    .aspectRatio(1, contentMode: .fit)
                    .id("comparison_empty_\(emptyIndex)")
            }
        }
    }
    
    // MARK: - 图例
    private var legend: some View {
        HStack(spacing: 4) {
            Text("少")
                .font(.caption2)
                .foregroundColor(.secondary)

            ForEach(0..<5) { level in
                Rectangle()
                    .fill(getColor(for: level))
                    .frame(width: 10, height: 10)
                    .cornerRadius(2)
            }

            Text("多")
                .font(.caption2)
                .foregroundColor(.secondary)

            Spacer()

            if let selectedDate = selectedDate {
                Text(formatDate(selectedDate))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - 计算属性
    private var weeksCount: Int {
        Int(ceil(Double(dateRange.count) / Double(columns)))
    }
    
    private var monthPositions: [(month: Int, name: String, position: Int)] {
        var positions: [(month: Int, name: String, position: Int)] = []
        let calendar = Calendar.current
        
        for (index, date) in dateRange.enumerated() {
            let month = calendar.component(.month, from: date)
            let day = calendar.component(.day, from: date)
            
            // 每月第一天或者每隔4周显示月份
            if day == 1 || (index > 0 && index % 28 == 0) {
                let monthName = calendar.shortMonthSymbols[month - 1]
                let position = index / columns
                positions.append((month: month, name: monthName, position: position))
            }
        }
        
        return positions
    }
    
    // MARK: - 辅助方法
    private func getIntensity(for date: Date, in dateRange: [Date]) -> Int {
        let calendar = Calendar.current
        let dayStart = calendar.startOfDay(for: date)

        // 检查是否有完成记录
        let hasLog = habit.logs.contains { log in
            calendar.isDate(log.date, inSameDayAs: dayStart) && log.done
        }

        return hasLog ? 4 : 0
    }
    
    private func getColor(for intensity: Int) -> Color {
        let baseColor = Color(hex: habit.colorHex)
        
        switch intensity {
        case 0:
            return Color.gray.opacity(0.1)
        case 1:
            return baseColor.opacity(0.3)
        case 2:
            return baseColor.opacity(0.5)
        case 3:
            return baseColor.opacity(0.7)
        case 4:
            return baseColor.opacity(1.0)
        default:
            return Color.gray.opacity(0.1)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日"
        return formatter.string(from: date)
    }
}

#Preview {
    let habit = Habit(name: "阅读", colorHex: "#007AFF")
    
    // 创建一些测试数据
    let calendar = Calendar.current
    let today = Date()
    let startDate = calendar.date(byAdding: .day, value: -89, to: today) ?? today
    
    var dates: [Date] = []
    var currentDate = startDate
    
    while currentDate <= today {
        dates.append(currentDate)
        currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
    }
    
    // 添加一些随机的完成记录
    for i in stride(from: 0, to: dates.count, by: 3) {
        let log = HabitLog(date: dates[i], done: true)
        log.habit = habit
        habit.logs.append(log)
    }
    
    return HabitHeatmapView(habit: habit, dateRange: dates, showMonthLabels: true, showComparison: false)
        .padding()
}

// MARK: - 扩展

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
