//
//  HabitsView.swift
//  TimeScale
//
//  Created by MeeY<PERSON> on 2025/8/25.
//

import SwiftUI
import SwiftData

struct HabitsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(filter: #Predicate<Habit> { !$0.archived }, sort: \Habit.createdAt) 
    private var habits: [Habit]
    
    @State private var showingAddHabit = false
    
    var body: some View {
        NavigationView {
            VStack {
                if habits.isEmpty {
                    // 空状态
                    VStack(spacing: 20) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("还没有习惯")
                            .font(.title2)
                            .fontWeight(.medium)
                        
                        Text("创建你的第一个习惯，开始养成好习惯的旅程")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("创建习惯") {
                            showingAddHabit = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 习惯列表
                    List {
                        ForEach(habits) { habit in
                            NavigationLink(destination: HabitDetailView(habit: habit)) {
                                HabitRowView(habit: habit)
                            }
                        }
                        .onDelete(perform: deleteHabits)
                    }
                }
            }
            .navigationTitle("习惯")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingAddHabit = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddHabit) {
                AddHabitView()
            }
        }
    }
    
    private func deleteHabits(offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                let habit = habits[index]
                habit.archived = true
                habit.updatedAt = .now
            }
            try? modelContext.save()
        }
    }
}

// MARK: - 习惯行视图
struct HabitRowView: View {
    let habit: Habit
    @Environment(\.modelContext) private var modelContext
    
    private var todayLog: HabitLog? {
        let today = Calendar.current.startOfDay(for: .now)
        return habit.logs.first { Calendar.current.isDate($0.date, inSameDayAs: today) }
    }
    
    private var isCompletedToday: Bool {
        todayLog?.done ?? false
    }

    // 修改 - 判断是否正在连续（今天已完成且有连续天数）
    private var isActiveStreak: Bool {
        return isCompletedToday && habit.currentStreak > 0
    }

    // 修改 - 判断是否之前连续但今天未连续（今天未完成但之前有连续天数）
    private var isPreviousStreak: Bool {
        return !isCompletedToday && habit.previousStreak > 0
    }

    // 新增 - 获取要显示的连续天数
    private var displayStreak: Int {
        if isCompletedToday {
            return habit.currentStreak
        } else {
            return habit.previousStreak
        }
    }

    var body: some View {
        HStack {
            // 习惯颜色指示器
            Circle()
                .fill(Color(hex: habit.colorHex))
                .frame(width: 12, height: 12)

            VStack(alignment: .leading, spacing: 4) {
                Text(habit.name)
                    .font(.headline)

                HStack {
                    // 修改 - 添加🔥图标视觉指示器来区分连续状态
                    HStack(spacing: 4) {
                        if isActiveStreak {
                            // 正在连续 - 使用彩色🔥图标
                            Text("🔥")
                                .font(.caption)
                        } else if isPreviousStreak {
                            // 之前连续但今天未连续 - 使用灰色🔥图标
                            Text("🔥")
                                .font(.caption)
                                .opacity(0.4)
                        }

                        // 修改 - 显示正确的连续天数（当前连续、之前连续或中断天数）
                        if displayStreak > 0 {
                            if isActiveStreak {
                                Text("\(displayStreak) 天连续")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else if isPreviousStreak {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("之前 \(displayStreak) 天连续")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    // 新增 - 显示已中断天数
                                    if habit.daysSinceLastStreak > 0 {
                                        Text("已中断 \(habit.daysSinceLastStreak) 天")
                                            .font(.caption2)
                                            .foregroundColor(.orange)
                                    }
                                }
                            }
                        } else {
                            Text("开始连续打卡")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    Text(habit.scheduleRule.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(4)
                }
            }
            
            Spacer()
            
            // 打卡按钮
            Button {
                toggleTodayCompletion()
            } label: {
                Image(systemName: isCompletedToday ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(isCompletedToday ? Color(hex: habit.colorHex) : .gray)
            }
            .buttonStyle(.plain)
        }
        .padding(.vertical, 4)
    }
    
    private func toggleTodayCompletion() {
        let today = Calendar.current.startOfDay(for: .now)
        
        if let existingLog = todayLog {
            // 切换现有记录的状态
            existingLog.done.toggle()
        } else {
            // 创建新的打卡记录
            let newLog = HabitLog(date: today, done: true)
            newLog.habit = habit
            modelContext.insert(newLog)
        }
        
        habit.updatedAt = .now
        try? modelContext.save()
    }
}

// MARK: - 颜色扩展
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    HabitsView()
        .modelContainer(for: [Habit.self, HabitLog.self], inMemory: true)
}
