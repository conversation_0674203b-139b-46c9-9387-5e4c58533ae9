//
//  PeriodComparisonChart.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/27.
//

import SwiftUI
import SwiftData
import Charts

// MARK: - 周期对比图表组件
struct PeriodComparisonChart: View {
    let habits: [Habit]
    let period: AnalysisPeriod
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("\(period.displayName)度习惯完成率对比")
                .font(.headline)
            
            if habits.isEmpty {
                emptyStateView
            } else {
                chartView
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "chart.bar")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            
            Text("暂无习惯数据")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
    }
    
    private var chartView: some View {
        ChartContainer(fullscreenTitle: "\(period.displayName)度习惯完成率对比", contentWidth: max(CGFloat(chartData.count) * 80, 600), fullscreenContentWidth: nil) {
            Chart(chartData) { data in
                BarMark(
                    x: .value("习惯", data.habitName),
                    y: .value("完成率", data.completionRate)
                )
                .foregroundStyle(Color(hex: data.colorHex))
                .opacity(0.8)
            }
            .frame(height: 200)
            .chartYScale(domain: 0...1)
            .chartYAxis {
                AxisMarks(values: [0, 0.25, 0.5, 0.75, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let doubleValue = value.as(Double.self) {
                            Text("\(Int(doubleValue * 100))%")
                        }
                    }
                }
            }
            .chartXAxis {
                AxisMarks { value in
                    AxisGridLine()
                    AxisValueLabel()
                }
            }
        }
    }
    
    // MARK: - 数据计算
    
    private var chartData: [HabitPeriodData] {
        let calendar = Calendar.current
        let now = Date()
        
        // 计算当前周期的开始和结束日期
        let (startDate, endDate) = getCurrentPeriodRange(for: period, from: now)
        
        return habits.compactMap { habit in
            let periodLogs = habit.logs.filter { log in
                log.done && 
                log.date >= calendar.startOfDay(for: startDate) && 
                log.date <= calendar.startOfDay(for: endDate)
            }
            
            let totalDays = calendar.dateComponents([.day], from: startDate, to: min(endDate, now)).day ?? 1
            let completionRate = totalDays > 0 ? Double(periodLogs.count) / Double(totalDays) : 0
            
            return HabitPeriodData(
                habitName: habit.name,
                colorHex: habit.colorHex,
                completionRate: completionRate,
                completedDays: periodLogs.count,
                totalDays: totalDays
            )
        }
        .sorted { $0.completionRate > $1.completionRate }
    }
    
    private func getCurrentPeriodRange(for period: AnalysisPeriod, from date: Date) -> (Date, Date) {
        let calendar = Calendar.current
        
        switch period {
        case .daily:
            let start = calendar.startOfDay(for: date)
            return (start, start)
            
        case .weekly:
            let weekInterval = calendar.dateInterval(of: .weekOfYear, for: date)
            return (weekInterval?.start ?? date, weekInterval?.end ?? date)
            
        case .monthly:
            let monthInterval = calendar.dateInterval(of: .month, for: date)
            return (monthInterval?.start ?? date, monthInterval?.end ?? date)
            
        case .quarterly:
            let month = calendar.component(.month, from: date)
            let year = calendar.component(.year, from: date)
            let quarterStartMonth = ((month - 1) / 3) * 3 + 1
            
            let quarterStart = calendar.date(from: DateComponents(year: year, month: quarterStartMonth, day: 1)) ?? date
            let quarterEnd = calendar.date(byAdding: .month, value: 3, to: quarterStart)?.addingTimeInterval(-1) ?? date
            
            return (quarterStart, quarterEnd)
            
        case .yearly:
            let yearInterval = calendar.dateInterval(of: .year, for: date)
            return (yearInterval?.start ?? date, yearInterval?.end ?? date)
        }
    }
}

// MARK: - 习惯周期数据模型
struct HabitPeriodData: Identifiable {
    let id = UUID()
    let habitName: String
    let colorHex: String
    let completionRate: Double
    let completedDays: Int
    let totalDays: Int
}

// MARK: - 多周期对比视图
struct MultiPeriodComparisonView: View {
    let habits: [Habit]
    // 修改 - 改为单项选择，仅展示一种周期类型（Confirmed via mcp-feedback-enhanced）
    @State private var selectedPeriod: AnalysisPeriod = .weekly

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 周期选择器（单选 Segmented）
            VStack(alignment: .leading, spacing: 12) {
                Text("对比周期")
                    .font(.headline)

                Picker("对比周期", selection: $selectedPeriod) {
                    ForEach(AnalysisPeriod.allCases, id: \.self) { period in
                        Text(period.displayName).tag(period)
                    }
                }
                .pickerStyle(.segmented)
            }

            // 对比图表（仅展示当前选择的一种类型）
            PeriodComparisonChart(habits: habits, period: selectedPeriod)
        }
    }
}

// MARK: - 周期趋势洞察组件
struct PeriodTrendInsight: View {
    let habits: [Habit]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("周期性洞察")
                .font(.headline)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(insights, id: \.title) { insight in
                    InsightCard(
                        title: insight.title,
                        value: insight.value,
                        description: insight.description,
                        color: insight.color,
                        icon: insight.icon
                    )
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var insights: [TrendInsight] {
        var results: [TrendInsight] = []
        
        // 最活跃的习惯
        if let mostActive = habits.max(by: { $0.currentStreak < $1.currentStreak }) {
            results.append(TrendInsight(
                title: "最活跃习惯",
                value: mostActive.name,
                description: "\(mostActive.currentStreak)天连续",
                color: .green,
                icon: "flame.fill"
            ))
        }
        
        // 需要关注的习惯
        if let needsAttention = habits.min(by: { $0.currentStreak < $1.currentStreak }) {
            results.append(TrendInsight(
                title: "需要关注",
                value: needsAttention.name,
                description: "连续天数较低",
                color: .orange,
                icon: "exclamationmark.triangle.fill"
            ))
        }
        
        // 平均完成率
        let totalLogs = habits.flatMap { $0.logs.filter { $0.done } }.count
        let totalDays = habits.count * 30 // 简化计算
        let avgRate = totalDays > 0 ? Double(totalLogs) / Double(totalDays) : 0
        
        results.append(TrendInsight(
            title: "整体完成率",
            value: "\(Int(avgRate * 100))%",
            description: "最近30天平均",
            color: .blue,
            icon: "chart.pie.fill"
        ))
        
        // 稳定性评分
        let streaks = habits.map { $0.currentStreak }
        let avgStreak = streaks.isEmpty ? 0 : streaks.reduce(0, +) / streaks.count
        let stability = avgStreak > 7 ? "优秀" : avgStreak > 3 ? "良好" : "需改进"
        
        results.append(TrendInsight(
            title: "稳定性",
            value: stability,
            description: "平均连续\(avgStreak)天",
            color: avgStreak > 7 ? .green : avgStreak > 3 ? .yellow : .red,
            icon: "chart.line.uptrend.xyaxis"
        ))
        
        return results
    }
}

// MARK: - 趋势洞察数据模型
struct TrendInsight {
    let title: String
    let value: String
    let description: String
    let color: Color
    let icon: String
}

// MARK: - 洞察卡片组件
struct InsightCard: View {
    let title: String
    let value: String
    let description: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Spacer()
                
                Circle()
                    .fill(color)
                    .frame(width: 8, height: 8)
            }
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(description)
                .font(.caption2)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Habit.self, HabitLog.self, configurations: config)
    
    let habit1 = Habit(name: "阅读", colorHex: "#007AFF")
    let habit2 = Habit(name: "运动", colorHex: "#FF3B30")
    container.mainContext.insert(habit1)
    container.mainContext.insert(habit2)
    
    return VStack {
        PeriodComparisonChart(habits: [habit1, habit2], period: .weekly)
        PeriodTrendInsight(habits: [habit1, habit2])
    }
    .modelContainer(container)
    .padding()
}
