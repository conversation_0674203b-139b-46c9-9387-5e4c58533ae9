//
//  ChartFullScreenUIKit.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/28.
//

import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - 基于 UIKit 的全屏横屏展示包装器
// 说明：相比直接强制设备旋转，使用自定义 HostingController 限定横屏，体验更稳定
struct ChartFullScreenUIKit<Content: View>: UIViewControllerRepresentable {
    let title: String
    @ViewBuilder var content: () -> Content

    func makeUIViewController(context: Context) -> UINavigationController {
        #if canImport(UIKit)
        let hosting = LandscapeHostingController(rootView:
            AnyView(
                ZStack {
                    Color(.systemBackground).ignoresSafeArea()
                    VStack(spacing: 0) {
                        HStack {
                            Button(action: { context.coordinator.dismiss() }) {
                                Image(systemName: "xmark.circle.fill").font(.title2)
                            }
                            .padding(.leading, 8)

                            Spacer()
                            Text(title).font(.headline)
                            Spacer()

                            Color.clear.frame(width: 28, height: 28)
                        }
                        .padding(.horizontal)
                        
                        ScrollView(.horizontal, showsIndicators: true) {
                            content().padding()
                        }
                        .ignoresSafeArea(edges: .bottom)
                    }
                }
            )
        )
        let nav = UINavigationController(rootViewController: hosting)
        nav.modalPresentationStyle = .fullScreen
        context.coordinator.nav = nav
        return nav
        #else
        return UINavigationController()
        #endif
    }

    func updateUIViewController(_ uiViewController: UINavigationController, context: Context) {}

    func makeCoordinator() -> Coordinator { Coordinator() }

    final class Coordinator {
        weak var nav: UINavigationController?
        @objc func dismiss() { nav?.dismiss(animated: true) }
    }
}

#if canImport(UIKit)
// 仅横屏的 HostingController
final class LandscapeHostingController: UIHostingController<AnyView> {
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask { [.landscapeLeft, .landscapeRight] }
    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation { .landscapeRight }
}
#endif

