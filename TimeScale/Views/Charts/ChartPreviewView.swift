//
//  ChartPreviewView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import Charts

// MARK: - 图表预览视图

struct ChartPreviewView: View {
    let habit: Habit
    let dateRange: [Date]
    let height: CGFloat
    
    init(habit: Habit, dateRange: [Date], height: CGFloat = 120) {
        self.habit = habit
        self.dateRange = dateRange
        self.height = height
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            HStack {
                Text("完成趋势预览")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text("\(completedDays)/\(dateRange.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 迷你图表
            Chart(chartData) { data in
                AreaMark(
                    x: .value("日期", data.date),
                    y: .value("完成", data.value)
                )
                .foregroundStyle(Color(hex: habit.colorHex).opacity(0.3))
                
                LineMark(
                    x: .value("日期", data.date),
                    y: .value("完成", data.value)
                )
                .foregroundStyle(Color(hex: habit.colorHex))
                .lineStyle(StrokeStyle(lineWidth: 2))
                .symbol(Circle().strokeBorder(lineWidth: 2))
                .symbolSize(30)
            }
            .frame(height: height)
            .chartYScale(domain: 0...1)
            .chartXAxis(.hidden)
            .chartYAxis(.hidden)
            .chartBackground { chartProxy in
                // 添加网格线
                Rectangle()
                    .fill(Color.clear)
                    .overlay(
                        VStack {
                            Divider().opacity(0.3)
                            Spacer()
                            Divider().opacity(0.3)
                        }
                    )
            }
            
            // 简单统计
            HStack {
                Label("\(Int(completionRate * 100))%", systemImage: "percent")
                    .font(.caption)
                    .foregroundColor(completionRate > 0.7 ? .green : completionRate > 0.4 ? .orange : .red)
                
                Spacer()
                
                if habit.currentStreak > 0 {
                    Label("\(habit.currentStreak)天", systemImage: "flame.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 数据计算
    
    private var chartData: [HabitChartData] {
        let calendar = Calendar.current
        return dateRange.map { date in
            let isCompleted = habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
            return HabitChartData(
                date: date,
                value: isCompleted ? 1.0 : 0.0,
                category: "完成",
                color: Color(hex: habit.colorHex)
            )
        }
    }
    
    private var completedDays: Int {
        let calendar = Calendar.current
        return dateRange.filter { date in
            habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
        }.count
    }
    
    private var completionRate: Double {
        guard !dateRange.isEmpty else { return 0 }
        return Double(completedDays) / Double(dateRange.count)
    }
}
