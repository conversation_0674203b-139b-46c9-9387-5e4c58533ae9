//
//  ChartContainer.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/28.
//

import SwiftUI

// MARK: - 通用图表容器
// 作用：
// 1) 提供水平滚动以避免坐标轴内容拥挤
// 2) 右上角固定全屏按钮，进入横屏全屏查看
struct ChartContainer<Content: View>: View {
    let fullscreenTitle: String
    let contentWidth: CGFloat? // 传入期望内容宽度（可为 nil）
    let fullscreenContentWidth: CGFloat? // 全屏时的内容宽度（可为 nil）
    @ViewBuilder var content: () -> Content

    @State private var showFullScreen = false

    var body: some View {
        ZStack(alignment: .topTrailing) {
            // 水平滚动容器，避免横轴过密
            ScrollView(.horizontal, showsIndicators: false) {
                content()
                    .frame(width: contentWidth)
            }

            // 全屏按钮
            Button {
                showFullScreen = true
            } label: {
                Image(systemName: "arrow.up.left.and.arrow.down.right")
                    .font(.system(size: 14, weight: .semibold))
                    .padding(8)
                    .background(.ultraThinMaterial)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            }
            .padding(.top, 2)
            .padding(.trailing, 2)
            .zIndex(1)
        }
        .fullScreenCover(isPresented: $showFullScreen) {
            // 使用 UIKit 包装器，锁定横屏全屏（修复：进入全屏未横屏的问题）
            ChartFullScreenUIKit(title: fullscreenTitle) {
                content()
                    .frame(width: fullscreenContentWidth ?? contentWidth)
            }
        }
    }
}

