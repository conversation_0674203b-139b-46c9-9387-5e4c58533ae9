//
//  ChartFullScreenView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/28.
//

import SwiftUI
import Charts

// MARK: - 全屏图表容器（横屏展示）
// 说明：用于在全屏中以横屏模式展示任何传入的图表内容
struct ChartFullScreenView<Content: View>: View {
    let title: String
    @ViewBuilder var content: () -> Content
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        ZStack {
            Color(.systemBackground).ignoresSafeArea()
            VStack(spacing: 0) {
                // 顶部工具栏
                HStack {
                    Button {
                        dismiss()
                        OrientationManager.forcePortrait() // 退出时恢复竖屏
                    } label: {
                        Image(systemName: "xmark.circle.fill").font(.title2)
                    }
                    .padding(.leading, 8)

                    Spacer()

                    Text(title)
                        .font(.headline)
                        .padding(.vertical, 8)

                    Spacer()

                    // 占位保持标题居中
                    Color.clear.frame(width: 28, height: 28)
                }
                .padding(.horizontal)

                // 图表内容（横屏时利用宽度）
                content()
                    .padding()
                    .ignoresSafeArea(edges: .bottom)
            }
        }
        .onAppear {
            // 进入时强制横屏
            OrientationManager.forceLandscape()
        }
        .onDisappear {
            OrientationManager.forcePortrait()
        }
    }
}

