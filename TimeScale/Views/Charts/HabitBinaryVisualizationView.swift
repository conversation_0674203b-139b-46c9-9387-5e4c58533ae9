//
//  HabitBinaryVisualizationView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/28.
//

import SwiftUI

// MARK: - 二元状态数据可视化组件
// 专门为习惯完成/未完成的二元状态设计的可视化方案
// 替换原有的SwiftUI Charts实现，提供更直观的展示效果

struct HabitBinaryVisualizationView: View {
    let habit: Habit
    let dateRange: [Date]
    let comparisonRange: [Date]?
    let visualizationType: BinaryVisualizationType
    
    @Environment(\.dismiss) private var dismiss
    
    init(habit: Habit, dateRange: [Date], comparisonRange: [Date]? = nil, visualizationType: BinaryVisualizationType = .progressRing) {
        self.habit = habit
        self.dateRange = dateRange
        self.comparisonRange = comparisonRange
        self.visualizationType = visualizationType
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题区域
                    headerSection
                    
                    // 主要可视化区域
                    mainVisualizationSection
                    
                    // 统计摘要
                    statisticsSection
                    
                    // 趋势分析
                    trendAnalysisSection
                }
                .padding()
            }
            .navigationTitle("\(habit.name) 数据分析")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 标题区域
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Circle()
                    .fill(Color(hex: habit.colorHex))
                    .frame(width: 12, height: 12)
                
                Text(habit.name)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(dateRangeDescription)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 主要可视化区域
    private var mainVisualizationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("完成情况")
                .font(.headline)
            
            switch visualizationType {
            case .progressRing:
                progressRingView
            case .statusGrid:
                statusGridView
            case .streakVisualization:
                streakVisualizationView
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 环形进度图
    private var progressRingView: some View {
        VStack(spacing: 20) {
            // 主要进度环
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 20)
                    .frame(width: 200, height: 200)
                
                Circle()
                    .trim(from: 0, to: completionRate)
                    .stroke(
                        Color(hex: habit.colorHex),
                        style: StrokeStyle(lineWidth: 20, lineCap: .round)
                    )
                    .frame(width: 200, height: 200)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: completionRate)
                
                VStack {
                    Text("\(Int(completionRate * 100))%")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: habit.colorHex))
                    
                    Text("完成率")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 对比环形图（如果有对比数据）
            if let comparisonRate = comparisonCompletionRate {
                HStack(spacing: 40) {
                    VStack {
                        ZStack {
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 8)
                                .frame(width: 80, height: 80)
                            
                            Circle()
                                .trim(from: 0, to: completionRate)
                                .stroke(Color(hex: habit.colorHex), style: StrokeStyle(lineWidth: 8, lineCap: .round))
                                .frame(width: 80, height: 80)
                                .rotationEffect(.degrees(-90))
                            
                            Text("\(Int(completionRate * 100))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                        }
                        Text("当前周期")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack {
                        ZStack {
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 8)
                                .frame(width: 80, height: 80)
                            
                            Circle()
                                .trim(from: 0, to: comparisonRate)
                                .stroke(Color.orange, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                                .frame(width: 80, height: 80)
                                .rotationEffect(.degrees(-90))
                            
                            Text("\(Int(comparisonRate * 100))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                        }
                        Text("对比周期")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 状态网格
    private var statusGridView: some View {
        VStack(spacing: 16) {
            // 网格展示
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 4), count: 7), spacing: 4) {
                ForEach(Array(dateRange.enumerated()), id: \.offset) { index, date in
                    let isCompleted = isDateCompleted(date)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(isCompleted ? Color(hex: habit.colorHex) : Color.gray.opacity(0.2))
                        .frame(height: 20)
                        .overlay(
                            Text("\(Calendar.current.component(.day, from: date))")
                                .font(.caption2)
                                .foregroundColor(isCompleted ? .white : .secondary)
                        )
                }
            }
            
            // 图例
            HStack {
                HStack(spacing: 4) {
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color(hex: habit.colorHex))
                        .frame(width: 12, height: 12)
                    Text("已完成")
                        .font(.caption)
                }
                
                HStack(spacing: 4) {
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 12, height: 12)
                    Text("未完成")
                        .font(.caption)
                }
                
                Spacer()
            }
        }
    }
    
    // MARK: - 连续性可视化
    private var streakVisualizationView: some View {
        VStack(spacing: 16) {
            // 连续天数展示
            HStack {
                VStack {
                    Text("\(habit.currentStreak)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(Color(hex: habit.colorHex))
                    Text("当前连续")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack {
                    Text("\(habit.longestStreak)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("最长连续")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 连续性条形图
            VStack(alignment: .leading, spacing: 8) {
                Text("连续性趋势")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack(spacing: 2) {
                    ForEach(Array(dateRange.enumerated()), id: \.offset) { index, date in
                        let isCompleted = isDateCompleted(date)
                        
                        Rectangle()
                            .fill(isCompleted ? Color(hex: habit.colorHex) : Color.gray.opacity(0.2))
                            .frame(width: max(2, (UIScreen.main.bounds.width - 64) / CGFloat(dateRange.count)), height: 20)
                    }
                }
                .cornerRadius(2)
            }
        }
    }
}

// MARK: - 数据计算扩展
extension HabitBinaryVisualizationView {
    // 完成率
    private var completionRate: Double {
        guard !dateRange.isEmpty else { return 0 }
        let completedDays = dateRange.filter { isDateCompleted($0) }.count
        return Double(completedDays) / Double(dateRange.count)
    }
    
    // 对比周期完成率
    private var comparisonCompletionRate: Double? {
        guard let comparisonRange = comparisonRange, !comparisonRange.isEmpty else { return nil }
        let completedDays = comparisonRange.filter { isDateCompleted($0) }.count
        return Double(completedDays) / Double(comparisonRange.count)
    }
    
    // 检查日期是否完成
    private func isDateCompleted(_ date: Date) -> Bool {
        let calendar = Calendar.current
        return habit.logs.contains { log in
            calendar.isDate(log.date, inSameDayAs: date) && log.done
        }
    }
    
    // 日期范围描述
    private var dateRangeDescription: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        
        guard let startDate = dateRange.first, let endDate = dateRange.last else {
            return ""
        }
        
        let period = "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
        
        if let comparisonRange = comparisonRange,
           let compStartDate = comparisonRange.first,
           let compEndDate = comparisonRange.last {
            let compPeriod = "\(formatter.string(from: compStartDate)) - \(formatter.string(from: compEndDate))"
            return "\(period) vs \(compPeriod)"
        }
        
        return period
    }
    
    // 统计区域
    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("统计摘要")
                .font(.headline)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "完成天数",
                    value: "\(dateRange.filter { isDateCompleted($0) }.count)",
                    unit: "天",
                    color: .green
                )
                
                StatCard(
                    title: "完成率",
                    value: "\(Int(completionRate * 100))",
                    unit: "%",
                    color: .blue
                )
                
                StatCard(
                    title: "当前连续",
                    value: "\(habit.currentStreak)",
                    unit: "天",
                    color: Color(hex: habit.colorHex)
                )
                
                StatCard(
                    title: "最长连续",
                    value: "\(habit.longestStreak)",
                    unit: "天",
                    color: .orange
                )
            }
        }
    }
    
    // 趋势分析区域
    private var trendAnalysisSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("趋势分析")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 12) {
                if let comparisonRate = comparisonCompletionRate {
                    let change = completionRate - comparisonRate
                    let changeText = change > 0 ? "提升了" : "下降了"
                    let changeColor: Color = change > 0 ? .green : .red
                    
                    HStack {
                        Image(systemName: change > 0 ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                            .foregroundColor(changeColor)
                        
                        Text("相比上一周期\(changeText) \(String(format: "%.1f", abs(change * 100)))%")
                            .font(.subheadline)
                    }
                }
                
                // 连续性分析
                let recentStreak = calculateRecentStreak()
                if recentStreak > 0 {
                    HStack {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                        
                        Text("最近连续完成 \(recentStreak) 天")
                            .font(.subheadline)
                    }
                }
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    // 计算最近连续天数
    private func calculateRecentStreak() -> Int {
        let calendar = Calendar.current
        let sortedDates = dateRange.sorted(by: >)
        var streak = 0
        
        for date in sortedDates {
            if isDateCompleted(date) {
                streak += 1
            } else {
                break
            }
        }
        
        return streak
    }
}

// MARK: - 二元可视化类型枚举
enum BinaryVisualizationType: String, CaseIterable {
    case progressRing = "环形进度"
    case statusGrid = "状态网格"
    case streakVisualization = "连续性可视化"
    
    var icon: String {
        switch self {
        case .progressRing: return "circle.fill"
        case .statusGrid: return "grid"
        case .streakVisualization: return "flame.fill"
        }
    }
}
