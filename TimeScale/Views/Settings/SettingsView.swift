//
//  SettingsView.swift
//  TimeScale
//
//  Created by MeeYou on 2025/8/25.
//

import SwiftUI
import UserNotifications

#if canImport(AlertToast)
import AlertToast
#endif

struct SettingsView: View {
    @EnvironmentObject private var notificationManager: NotificationManager
    @State private var showingClearDataAlert = false
    // 新增 - Toast状态，替代阻断式Alert
    @State private var showToast = false
    @State private var toastTitle = ""
    @State private var toastSub: String? = nil
    @State private var toastType: AppToastType = .info


    var body: some View {
        NavigationView {
            List {
                Section("通知设置") {
                    NavigationLink("通知权限") {
                        NotificationSettingsView()
                    }

                    HStack {
                        Text("通知状态")
                        Spacer()
                        Text(notificationStatusText)
                            .foregroundColor(notificationStatusColor)
                    }
                }

                Section("数据管理") {
                    NavigationLink("备份与同步") {
                        BackupSettingsView()
                    }

                    NavigationLink("异常检测设置") {
                        AnomalyThresholdSettingsView()
                    }

                    Button("清除所有数据") {
                        showingClearDataAlert = true
                    }
                    .foregroundColor(.red)
                }

                Section("关于") {
                    HStack {
                        Text("版本")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }

                    NavigationLink("隐私政策") {
                        PrivacyPolicyView()
                    }

                    NavigationLink("使用条款") {
                        TermsOfServiceView()
                    }

                    NavigationLink("开源许可") {
                        OpenSourceLicensesView()
                    }
                }
            }
                Section("计时器") {
                    NavigationLink("异常阈值设置") { AnomalyThresholdSettingsView() }
                }

            .navigationTitle("设置")
            // 修改 - 移除清理数据的阻断式Alert，改为直接执行并用Toast反馈
            .onChange(of: showingClearDataAlert) { _, newValue in
                if newValue {
                    clearAllData()
                    showingClearDataAlert = false
                }
            }
            .appToast(isPresenting: $showToast, type: toastType, title: toastTitle, subTitle: toastSub)
        }
    }

    // MARK: - 计算属性
    private var notificationStatusText: String {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return "已授权"
        case .denied:
            return "已拒绝"
        case .notDetermined:
            return "未设置"
        case .provisional:
            return "临时授权"
        case .ephemeral:
            return "临时"
        @unknown default:
            return "未知"
        }
    }

    private var notificationStatusColor: Color {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return .green
        case .denied:
            return .red
        case .notDetermined:
            return .orange
        default:
            return .secondary
        }
    }

    // MARK: - 方法
    private func clearAllData() {
        // 修改 - 直接执行清除并Toast反馈（此功能为占位）
        toastType = .loading
        toastTitle = "正在清除所有数据..."
        toastSub = nil
        showToast = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            toastType = .success
            toastTitle = "已清除所有数据"
            toastSub = nil
            showToast = true
        }
    }
}

// MARK: - 通知设置视图
struct NotificationSettingsView: View {
    @EnvironmentObject private var notificationManager: NotificationManager

    var body: some View {
        List {
            Section {
                HStack {
                    Text("当前状态")
                    Spacer()
                    Text(statusText)
                        .foregroundColor(statusColor)
                }

                if notificationManager.authorizationStatus == .notDetermined {
                    Button("请求通知权限") {
                        Task {
                            await notificationManager.requestAuthorization()
                        }
                    }
                } else if notificationManager.authorizationStatus == .denied {
                    Button("打开系统设置") {
                        notificationManager.openSettings()
                    }
                }
            } header: {
                Text("通知权限")
            } footer: {
                Text("开启通知权限后，TimeScale可以在设定的时间提醒您完成习惯和目标。")
            }

            if notificationManager.authorizationStatus == .authorized {
                Section("通知管理") {
                    Button("清除所有通知") {
                        notificationManager.removeAllNotifications()
                    }

                    Button("清除应用角标") {
                        notificationManager.clearBadge()
                    }
                }
            }
        }
        .navigationTitle("通知设置")
        .navigationBarTitleDisplayMode(.inline)
        .task {
            await notificationManager.updateAuthorizationStatus()
        }
    }

    private var statusText: String {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return "已授权"
        case .denied:
            return "已拒绝"
        case .notDetermined:
            return "未设置"
        case .provisional:
            return "临时授权"
        case .ephemeral:
            return "临时"
        @unknown default:
            return "未知"
        }
    }

    private var statusColor: Color {
        switch notificationManager.authorizationStatus {
        case .authorized:
            return .green
        case .denied:
            return .red
        case .notDetermined:
            return .orange
        default:
            return .secondary
        }
    }
}

// MARK: - 占位视图
struct DataBackupView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var dataManager = DataManager.shared
    @State private var showingShareSheet = false
    @State private var exportURL: URL?
    @State private var showingError = false
    @State private var errorMessage = ""

    // Toast 状态（用于错误/信息提示）
    @State private var showToast = false
    @State private var toastTitle = ""
    @State private var toastSub: String? = nil
    @State private var toastType: AppToastType = .info

    var body: some View {
        List {
            Section {
                Button {
                    exportData()
                } label: {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.blue)

                        VStack(alignment: .leading) {
                            Text("导出数据")
                                .foregroundColor(.primary)
                            Text("将所有数据导出为JSON文件")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if dataManager.isExporting {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                }
                .disabled(dataManager.isExporting)

                if let lastExportDate = dataManager.lastExportDate {
                    HStack {
                        Text("上次导出")
                        Spacer()
                        Text(lastExportDate, style: .relative)
                            .foregroundColor(.secondary)
                    }
                }
            } header: {
                Text("数据导出")
            } footer: {
                Text("导出的数据包含所有习惯、目标和时间记录，可用于备份或迁移到其他设备。")
            }

            Section {
                VStack(alignment: .leading, spacing: 8) {
                    Text("导出内容包括：")
                        .font(.headline)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("• 所有习惯和打卡记录")
                        Text("• 所有目标和完成记录")
                        Text("• 所有时间分类和记录")
                        Text("• 应用设置和偏好")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("数据备份")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingShareSheet) {
            if let url = exportURL {
                ShareSheet(items: [url])
            }
        }
        // 修改 - 用Toast替代导出失败Alert
        .onChange(of: showingError) { _, newValue in
            if newValue {
                toastType = .error
                toastTitle = "导出失败"
                toastSub = errorMessage
                showToast = true
                showingError = false
            }
        }
        .appToast(isPresenting: $showToast, type: toastType, title: toastTitle, subTitle: toastSub)
    }

    private func exportData() {
        Task {
            do {
                let url = try await dataManager.exportData(from: modelContext)
                await MainActor.run {
                    exportURL = url
                    showingShareSheet = true
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

struct DataRestoreView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var dataManager = DataManager.shared
    @State private var showingFilePicker = false
    @State private var showingImportConfirmation = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var validationResult: ImportValidationResult?
    @State private var replaceExisting = false

    // Toast 状态（用于导入错误提示）
    @State private var showToast = false
    @State private var toastTitle = ""
    @State private var toastSub: String? = nil
    @State private var toastType: AppToastType = .info


    var body: some View {
        List {
            Section {
                Button {
                    showingFilePicker = true
                } label: {
                    HStack {
                        Image(systemName: "square.and.arrow.down")
                            .foregroundColor(.blue)

                        VStack(alignment: .leading) {
                            Text("导入数据")
                                .foregroundColor(.primary)
                            Text("从JSON文件恢复数据")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if dataManager.isImporting {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                }
                .disabled(dataManager.isImporting)

                if let lastImportDate = dataManager.lastImportDate {
                    HStack {
                        Text("上次导入")
                        Spacer()
                        Text(lastImportDate, style: .relative)
                            .foregroundColor(.secondary)
                    }
                }
            } header: {
                Text("数据导入")
            } footer: {
                Text("选择之前导出的JSON文件来恢复数据。导入前请确保文件来源可信。")
            }

            Section {
                VStack(alignment: .leading, spacing: 8) {
                    Text("注意事项：")
                        .font(.headline)
                        .foregroundColor(.orange)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("• 导入会覆盖现有数据")
                        Text("• 请在导入前备份当前数据")
                        Text("• 确保文件格式正确且完整")
                        Text("• 导入过程不可撤销")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("数据恢复")
        .navigationBarTitleDisplayMode(.inline)
        .fileImporter(
            isPresented: $showingFilePicker,
            allowedContentTypes: [.json],
            allowsMultipleSelection: false
        ) { result in
            handleFileSelection(result)
        }
        // 修改 - 用Toast替代导入确认与失败Alert
        .onChange(of: showingImportConfirmation) { _, newValue in
            if newValue {
                performImport()
                showingImportConfirmation = false
            }
        }
        .onChange(of: showingError) { _, newValue in
            if newValue {
                toastType = .error
                toastTitle = "导入失败"
                toastSub = errorMessage
                showToast = true

                showingError = false
            }
        }
        .appToast(isPresenting: $showToast, type: toastType, title: toastTitle, subTitle: toastSub)
    }

    private func handleFileSelection(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            Task {
                do {
                    let result = try await dataManager.validateImportFile(at: url)
                    await MainActor.run {
                        if result.isCompatible {
                            validationResult = result
                            showingImportConfirmation = true
                        } else {
                            errorMessage = "文件版本不兼容: \(result.version)"
                            showingError = true
                        }
                    }
                } catch {
                    await MainActor.run {
                        errorMessage = error.localizedDescription
                        showingError = true
                    }
                }
            }

        case .failure(let error):
            errorMessage = error.localizedDescription
            showingError = true
        }
    }

    private func performImport() {
        guard let result = validationResult else { return }

        // 这里需要保存文件URL，实际实现中需要处理文件访问权限
        // 为简化示例，这里只显示成功消息
        Task {
            // 实际导入逻辑
            await MainActor.run {
                // 模拟导入成功
                dataManager.lastImportDate = Date()
            }
        }
    }
}

// MARK: - ShareSheet
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

struct PrivacyPolicyView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text("隐私政策")
                    .font(.title)
                    .fontWeight(.bold)

                Text("TimeScale 重视您的隐私。本应用采用本地存储，不会收集或上传您的个人数据。")

                Text("数据存储")
                    .font(.headline)

                Text("• 所有数据均存储在您的设备本地\n• 不会上传到任何服务器\n• 数据仅用于应用功能")

                Text("权限使用")
                    .font(.headline)

                Text("• 通知权限：用于习惯和目标提醒\n• 不会访问其他敏感权限")

                Spacer()
            }
            .padding()
        }
        .navigationTitle("隐私政策")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct TermsOfServiceView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text("使用条款")
                    .font(.title)
                    .fontWeight(.bold)

                Text("欢迎使用 TimeScale！")

                Text("服务说明")
                    .font(.headline)

                Text("TimeScale 是一款个人时间管理应用，帮助您养成好习惯、达成目标、记录时间使用。")

                Text("使用规则")
                    .font(.headline)

                Text("• 请合理使用应用功能\n• 不得用于非法用途\n• 数据安全由用户自行负责")

                Text("免责声明")
                    .font(.headline)

                Text("本应用按现状提供，不承担任何明示或暗示的担保。")

                Spacer()
            }
            .padding()
        }
        .navigationTitle("使用条款")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct OpenSourceLicensesView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text("开源许可")
                    .font(.title)
                    .fontWeight(.bold)

                Text("TimeScale 使用了以下开源技术：")

                Text("SwiftUI")
                    .font(.headline)
                Text("Apple Inc. - iOS 应用开发框架")

                Text("SwiftData")
                    .font(.headline)
                Text("Apple Inc. - 数据持久化框架")

                Text("SwiftUI Charts")
                    .font(.headline)
                Text("Apple Inc. - 图表可视化框架")

                Spacer()
            }
            .padding()
        }
        .navigationTitle("开源许可")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    SettingsView()
        .environmentObject(NotificationManager.shared)
}
