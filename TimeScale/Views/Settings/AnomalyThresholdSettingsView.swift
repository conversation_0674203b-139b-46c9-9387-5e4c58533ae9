//
//  AnomalyThresholdSettingsView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI

struct AnomalyThresholdSettingsView: View {
    @AppStorage("anomalyThresholdHours") private var hours: Int = 12

    private let candidates: [Int] = [4, 6, 8, 10, 12, 16, 24]

    var body: some View {
        Form {
            Section("异常恢复阈值（小时）") {
                Picker("超过该时长将提示恢复", selection: $hours) {
                    ForEach(candidates, id: \.self) { h in
                        Text("\(h) 小时").tag(h)
                    }
                }
                .pickerStyle(.wheel)

                Text("当前设置：\(hours) 小时")
                    .foregroundStyle(.secondary)
            }

            Section(footer: Text("当计时在后台超过设定阈值，或应用异常终止后，下次进入时会弹出恢复提示。")) {
                EmptyView()
            }
        }
        .navigationTitle("异常阈值设置")
        .navigationBarTitleDisplayMode(.inline)
    }
}

