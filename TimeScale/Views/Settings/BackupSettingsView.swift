//
//  BackupSettingsView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import SwiftData

#if canImport(AlertToast)
import AlertToast
#endif
import os

struct BackupSettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var backupManager = iCloudBackupManager.shared
    @StateObject private var dataManager = DataManager.shared

    @State private var selectedBackup: BackupMetadata?
    @State private var exportURL: URL?

    // 新增 - Toast状态（替代Alert）
    // 修改 - 统一用非阻断式Toast反馈备份/恢复/删除/刷新等操作结果
    @State private var showToast = false
    @State private var toastTitle = ""
    @State private var toastSubTitle: String? = nil
    @State private var toastType: AppToastType = .info

    var body: some View {
        NavigationView {
            List {
                // iCloud状态部分
                iCloudStatusSection

                // 备份操作部分
                backupActionsSection

                // 自动同步设置
                autoSyncSection

                // 可用备份列表
                availableBackupsSection

                // 本地导出部分
                localExportSection
            }
            .navigationTitle("数据备份")
            .refreshable {
                await refreshBackups()
            }
            .onAppear {
                Task {
                    await refreshBackups()
                }
            }
            // 修改 - 移除阻断性弹窗，改为全局Toast（备份与同步）。Confirmed via mcp-feedback-enhanced
            .sheet(item: Binding<ShareableURL?>(
                get: { exportURL.map(ShareableURL.init) },
                set: { newValue in exportURL = newValue?.url }
            )) { shareableURL in
                ShareSheet(items: [shareableURL.url])
            }
        }
            // 新增 - 在NavigationView层绑定Toast
            .appToast(isPresenting: $showToast, type: toastType, title: toastTitle, subTitle: toastSubTitle, position: .topSafe, haptics: true)

    }

    // MARK: - iCloud状态部分
    private var iCloudStatusSection: some View {
        Section {
            HStack {
                Image(systemName: backupManager.iCloudAvailable ? "icloud.fill" : "icloud.slash")
                    .foregroundColor(backupManager.iCloudAvailable ? .blue : .red)

                VStack(alignment: .leading, spacing: 4) {
                    Text("iCloud状态")
                        .font(.headline)
                    Text(backupManager.iCloudAvailable ? "已连接" : "不可用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if backupManager.iCloudAvailable {
                    VStack(alignment: .trailing, spacing: 4) {
                        if let lastBackup = backupManager.lastBackupDate {
                            Text("上次备份")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(RelativeDateTimeFormatter().localizedString(for: lastBackup, relativeTo: Date()))
                                .font(.caption2)
                                .foregroundColor(.secondary)

                        } else {
                            Text("未备份")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        } header: {
            Text("云端备份")
        }
    }

    // MARK: - 备份操作部分
    private var backupActionsSection: some View {
        Section {
            // 立即备份按钮
            Button(action: {
                Task {
                    await performBackup()
                }
            }) {
                HStack {
                    Image(systemName: "icloud.and.arrow.up")
                        .foregroundColor(.blue)
                    Text("立即备份")
                    Spacer()
                    if backupManager.isBackingUp {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .disabled(!backupManager.iCloudAvailable || backupManager.isBackingUp)

            // 刷新备份列表
            Button(action: {
                Task {
                    await refreshBackups()
                }
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.green)
                    Text("刷新备份列表")
                    Spacer()
                }
            }
            .disabled(!backupManager.iCloudAvailable)

        } header: {
            Text("备份操作")
        }
    }

    // MARK: - 自动同步设置
    private var autoSyncSection: some View {
        Section {
            Toggle(isOn: $backupManager.syncEnabled) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.orange)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("自动同步")
                        Text("应用启动时自动备份数据")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .disabled(!backupManager.iCloudAvailable)
            .onChange(of: backupManager.syncEnabled) { _, newValue in
                backupManager.setSyncEnabled(newValue)
            }

            if backupManager.syncEnabled, let lastSync = backupManager.lastSyncDate {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.secondary)
                    Text("上次同步")
                    Spacer()
                    Text(RelativeDateTimeFormatter().localizedString(for: lastSync, relativeTo: Date()))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

        } header: {
            Text("同步设置")
        }
    }

    // MARK: - 可用备份列表
    private var availableBackupsSection: some View {
        Section {
            if backupManager.availableBackups.isEmpty {
                HStack {
                    Image(systemName: "tray")
                        .foregroundColor(.secondary)
                    Text("暂无备份")
                        .foregroundColor(.secondary)
                }
            } else {
                ForEach(backupManager.availableBackups) { backup in
                    BackupRowView(backup: backup) {
                        // 修改 - 直接恢复，不再弹出确认框，改Toast反馈
                        selectedBackup = backup
                        Task { await restoreFromBackup(backup) }
                    } onDelete: {
                        // 修改 - 直接删除，不再弹出确认框，改Toast反馈
                        selectedBackup = backup
                        Task { await deleteBackup(backup) }
                    }
                }
            }
        } header: {
            Text("可用备份 (\(backupManager.availableBackups.count))")
        }
    }

    // MARK: - 本地导出部分
    private var localExportSection: some View {
        Section {
            Button(action: {
                Task {
                    await exportLocalData()
                }
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(.purple)
                    Text("导出到文件")
                    Spacer()
                    if dataManager.isExporting {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .disabled(dataManager.isExporting)

        } header: {
            Text("本地导出")
        } footer: {
            Text("导出数据到本地文件，可用于手动备份或迁移到其他设备")
        }
    }

    // MARK: - 操作方法

    private func performBackup() async {
        AppLog.ui.log("点击立即备份按钮")
        do {
            toastType = .loading
            toastTitle = "正在备份到iCloud"
            toastSubTitle = "请保持网络连接"
            showToast = true

            try await backupManager.performBackup(from: modelContext)

            toastType = .success
            toastTitle = "已备份到iCloud"
            toastSubTitle = Date().formatted(date: .abbreviated, time: .shortened)
            showToast = true
        } catch {
            AppLog.ui.error("备份触发失败 | error=\(String(describing: error), privacy: .public)")
            toastType = .error
            toastTitle = "备份失败"
            toastSubTitle = error.localizedDescription
            showToast = true
        }
    }

    private func restoreFromBackup(_ backup: BackupMetadata) async {
        do {
            toastType = .loading
            toastTitle = "正在从iCloud恢复"
            toastSubTitle = backup.formattedDate
            showToast = true

            try await backupManager.restoreFromBackup(backup, to: modelContext, replaceExisting: true)

            toastType = .success
            toastTitle = "恢复完成"
            toastSubTitle = "所有数据已替换"
            showToast = true
        } catch {
            toastType = .error
            toastTitle = "恢复失败"
            toastSubTitle = error.localizedDescription
            showToast = true
        }
    }

    private func deleteBackup(_ backup: BackupMetadata) async {
        do {
            try await backupManager.deleteBackup(backup)
            toastType = .success
            toastTitle = "已删除备份"
            toastSubTitle = backup.formattedDate
            showToast = true
        } catch {
            toastType = .error
            toastTitle = "删除失败"
            toastSubTitle = error.localizedDescription
            showToast = true
        }
    }

    private func refreshBackups() async {
        do {
            try await backupManager.refreshAvailableBackups()
            toastType = .info
            toastTitle = "已更新备份列表"
            toastSubTitle = "共 \(backupManager.availableBackups.count) 个"
            showToast = true
        } catch {
            toastType = .error
            toastTitle = "刷新失败"
            toastSubTitle = error.localizedDescription
            showToast = true
        }
    }

    private func exportLocalData() async {
        do {
            let url = try await dataManager.exportData(from: modelContext)
            exportURL = url
            toastType = .success
            toastTitle = "已导出到文件"
            toastSubTitle = nil
            showToast = true
        } catch {
            toastType = .error
            toastTitle = "导出失败"
            toastSubTitle = error.localizedDescription
            showToast = true
        }
    }
}

// MARK: - 备份行视图

struct BackupRowView: View {
    let backup: BackupMetadata
    let onRestore: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 主要信息行
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(backup.formattedDate)
                        .font(.headline)

                    HStack(spacing: 12) {
                        Label(backup.formattedSize, systemImage: "doc")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Label(backup.deviceName, systemImage: "iphone")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if backup.isIncremental {
                            Label("增量", systemImage: "arrow.up.circle")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }

                Spacer()

                // 操作按钮
                HStack(spacing: 8) {
                    Button(action: onRestore) {
                        Image(systemName: "arrow.down.circle")
                            .foregroundColor(.blue)
                    }

                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                }
            }

            // 版本信息
            HStack {
                Text("应用版本: \(backup.version)")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                Text("数据版本: \(backup.dataVersion)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 辅助结构

struct ShareableURL: Identifiable {
    let id = UUID()
    let url: URL
}



// MARK: - 预览

#Preview {
    BackupSettingsView()
        .modelContainer(for: [Habit.self, HabitLog.self, Goal.self, GoalCheckIn.self, TimeCategory.self, TimeEntry.self], inMemory: true)
}
