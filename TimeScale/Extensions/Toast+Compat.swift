//
//  Toast+Compat.swift
//  TimeScale
//
//  兼容性Toast封装：优先使用 AlertToast 库；若不可用则回退到本地轻提示 overlay。
//  使用方式：在视图上调用 .appToast(...)
//

// 统一的Toast主题配置
struct ToastTheme {
    // 颜色遵循系统语义色，贴合苹果设计风格
    static var successColor: Color = .green    // systemGreen
    static var errorColor: Color = .red        // systemRed
    static var infoColor: Color = .blue        // systemBlue
    static var loadingColor: Color = .orange   // 用于本地fallback图标

    // 时长：短促且不打断
    static var successDuration: Double = 1.5
    static var infoDuration: Double = 1.2
    static var errorDuration: Double = 3.0
    // 加载态建议由逻辑结束时关闭，不自动消失
}

import SwiftUI
import UIKit

#if canImport(AlertToast)
import AlertToast
#endif

/// 统一的Toast类型
enum AppToastType {
    case success
    case error
    case loading
    case info
}

/// Toast 位置
enum AppToastPosition {
    case top        // 靠近顶部（可能与导航栏更近）
    case topSafe    // 顶部安全区下方（默认）
    case center
    case bottom
}

/// 将统一类型映射到第三方或本地渲染
fileprivate struct LocalToastModifier: ViewModifier {
    @Binding var isPresenting: Bool
    let type: AppToastType
    let title: String
    let subTitle: String?
    let autoDismiss: Bool
    let duration: Double
    let position: AppToastPosition
    let haptics: Bool
    let onTap: (() -> Void)?
    let completion: (() -> Void)?

    @State private var internalVisible: Bool = false

    func body(content: Content) -> some View {
        GeometryReader { geo in
            ZStack {
                content
                if internalVisible {
                    container(in: geo)
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .onTapGesture {
                            onTap?()
                            hide()
                        }
                        .zIndex(1)
                }
            }
            .onChange(of: isPresenting) { _, newValue in
                if newValue { show() } else { hide() }
            }
        }
    }

    private func container(in geo: GeometryProxy) -> some View {
        VStack {
            if position == .bottom { Spacer(minLength: 0) }
            toastView
            if position == .top || position == .topSafe { Spacer(minLength: 0) }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: alignment)
        .padding(.horizontal, 12)
        .padding(.top, topPadding(using: geo))
        .padding(.bottom, bottomPadding(using: geo))
    }

    private var toastView: some View {
        HStack(spacing: 10) {
            Image(systemName: iconName)
                .foregroundColor(iconColor)
            VStack(alignment: .leading, spacing: 2) {
                Text(title).font(.callout).foregroundColor(.white)
                if let sub = subTitle { Text(sub).font(.caption2).foregroundColor(Color.white.opacity(0.85)) }
            }
            Spacer()
            if case .loading = type { ProgressView().tint(.white).scaleEffect(0.9) }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 14)
        .background(Color.black.opacity(0.82)) // 提升对比度
        .overlay(
            RoundedRectangle(cornerRadius: 14)
                .stroke(Color.white.opacity(0.12), lineWidth: 0.5)
        )
        .clipShape(RoundedRectangle(cornerRadius: 14))
        .shadow(color: Color.black.opacity(0.25), radius: 12, x: 0, y: 8)
    }

    private var alignment: Alignment {
        switch position {
        case .top, .topSafe: return .top
        case .center: return .center
        case .bottom: return .bottom
        }
    }

    private func topPadding(using geo: GeometryProxy) -> CGFloat {
        switch position {
        case .top: return 8
        case .topSafe: return geo.safeAreaInsets.top + 8
        case .center: return 0
        case .bottom: return 0
        }
    }

    private func bottomPadding(using geo: GeometryProxy) -> CGFloat {
        switch position {
        case .bottom: return geo.safeAreaInsets.bottom + 12
        default: return 0
        }
    }

    private var iconName: String {
        switch type {
        case .success: return "checkmark.circle.fill"
        case .error: return "xmark.octagon.fill"
        case .loading: return "arrow.triangle.2.circlepath.circle.fill"
        case .info: return "info.circle.fill"
        }
    }
    private var iconColor: Color {
        switch type {
        case .success: return .green
        case .error: return .red
        case .loading: return .orange
        case .info: return .blue
        }
    }

    private func show() {
        withAnimation(.spring(response: 0.35, dampingFraction: 0.85)) {
            internalVisible = true
        }
        if haptics {
            switch type {
            case .success:
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
            case .error:
                UINotificationFeedbackGenerator().notificationOccurred(.error)
            case .info:
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
            case .loading:
                break
            }
        }
        // 自动消失
        if autoDismiss && type != .loading {
            DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                hide()
            }
        }
    }

    private func hide() {
        withAnimation(.easeInOut(duration: 0.25)) {
            internalVisible = false
        }
        isPresenting = false
        completion?()
    }
}

extension View {
    /// 统一的Toast呈现API。优先使用第三方AlertToast；否则回退到本地overlay，不阻断交互。
    @ViewBuilder
    func appToast(
        isPresenting: Binding<Bool>,
        type: AppToastType,
        title: String,
        subTitle: String? = nil,
        autoDismiss: Bool? = nil,
        duration: Double? = nil,
        position: AppToastPosition = .topSafe,
        haptics: Bool = true,
        onTap: (() -> Void)? = nil,
        completion: (() -> Void)? = nil
    ) -> some View {
        // 规则：加载态不自动消失；成功/信息/错误按主题时长
        let isLoading = (type == .loading)
        let resolvedDuration: Double? = {
            if let duration { return duration }
            if isLoading { return nil }
            switch type {
            case .success: return ToastTheme.successDuration
            case .info: return ToastTheme.infoDuration
            case .error: return ToastTheme.errorDuration
            case .loading: return nil
            }
        }()
        let resolvedAutoDismiss: Bool = autoDismiss ?? !isLoading

        #if canImport(AlertToast)
        self.toast(isPresenting: isPresenting, duration: resolvedDuration ?? 0, tapToDismiss: true) {
            // {{ Source: context7-mcp on 'AlertToast usage' }}
            let alertType: AlertToast.AlertType
            switch type {
            case .success: alertType = .complete(ToastTheme.successColor)
            case .error: alertType = .error(ToastTheme.errorColor)
            case .loading: alertType = .loading
            case .info: alertType = .systemImage("info.circle.fill", ToastTheme.infoColor)
            }
            let display: AlertToast.DisplayMode = isLoading ? .hud : .banner(.pop)
            return AlertToast(displayMode: display, type: alertType, title: title, subTitle: subTitle)
        } onTap: {
            onTap?()
        } completion: {
            completion?()
        }
        #else
        self.modifier(LocalToastModifier(
            isPresenting: isPresenting,
            type: type,
            title: title,
            subTitle: subTitle,
            autoDismiss: resolvedAutoDismiss,
            duration: resolvedDuration ?? ToastTheme.successDuration,
            position: position,
            haptics: haptics,
            onTap: onTap,
            completion: completion
        ))
        #endif
    }
}

