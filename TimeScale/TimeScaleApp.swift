//
//  TimeScaleApp.swift
//  TimeScale
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/12.
//

import SwiftUI
import SwiftData
import UserNotifications

@main
struct TimeScaleApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Habit.self,
            HabitLog.self,
            Goal.self,
            GoalCheckIn.self,
            TimeCategory.self,
            TimeEntry.self,
            AppTimerState.self // 新增 - 应用级计时状态模型，用于后台与恢复
        ])

        // 添加版本控制和迁移策略
        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true,
            cloudKitDatabase: .none // 暂时禁用CloudKit直接集成，使用我们的iCloud备份
        )

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            // 如果创建失败，尝试删除旧数据库并重新创建
            print("ModelContainer creation failed: \(error)")
            print("Attempting to reset database...")

            // 删除旧的数据库文件
            let storeURL = modelConfiguration.url
            try? FileManager.default.removeItem(at: storeURL)
            try? FileManager.default.removeItem(at: storeURL.appendingPathExtension("wal"))
            try? FileManager.default.removeItem(at: storeURL.appendingPathExtension("shm"))

            // 重新尝试创建
            do {
                return try ModelContainer(for: schema, configurations: [modelConfiguration])
            } catch {
                fatalError("Could not create ModelContainer after reset: \(error)")
            }
        }
    }()

    var body: some Scene {
        WindowGroup {
            RootLifecycleBridge() // 使用根级生命周期桥接 + 主界面
                .environmentObject(NotificationManager.shared)
                .onAppear {
                    setupNotifications()
                    setupAutoSync()
                }
        }
        .modelContainer(sharedModelContainer)
    }

    // MARK: - 通知设置
    private func setupNotifications() {
        // 设置通知代理
        UNUserNotificationCenter.current().delegate = NotificationDelegate.shared

        // 更新权限状态
        Task {
            await NotificationManager.shared.updateAuthorizationStatus()
        }
    }

    // MARK: - 自动同步设置
    private func setupAutoSync() {
        Task {
            await MainActor.run {
                // 启动时执行自动同步
                Task {
                    try? await iCloudBackupManager.shared.performAutoSync()
                }
            }
        }
    }
}

// MARK: - 通知代理
class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    static let shared = NotificationDelegate()

    // 应用在前台时收到通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }

    // 用户点击通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        Task {
            await NotificationManager.shared.handleNotificationResponse(response)
        }
        completionHandler()
    }
}
